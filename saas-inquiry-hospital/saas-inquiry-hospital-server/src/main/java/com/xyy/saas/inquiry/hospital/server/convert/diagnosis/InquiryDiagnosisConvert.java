package com.xyy.saas.inquiry.hospital.server.convert.diagnosis;

import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisExcelVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDO;
import com.xyy.saas.inquiry.pojo.diagnosis.InquiryDiagnosisSearchRespDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchRespDto;
import com.xyy.saas.transmitter.api.dict.dto.TransmissionOrganDictDTO;
import com.xyy.saas.transmitter.enums.DictTypeConstants;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 诊断信息
 *
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryDiagnosisConvert {

    InquiryDiagnosisConvert INSTANCE = Mappers.getMapper(InquiryDiagnosisConvert.class);

    List<InquiryDiagnosisRespVO> convertVos(List<InquiryDiagnosisDO> inquiryDiagnosisDOS);

    InquiryDiagnosisSaveReqVO convertExcelVO(InquiryDiagnosisExcelVO excelVO);

    InquiryDiagnosisDO convertDo(InquiryDiagnosisSaveReqVO r);

    InquiryDiagnosisDto convertPageDto(InquiryDiagnosisPageReqVO pageReqVO);

    List<InquiryDiagnosisRespVO> convertDictDtoRespVos(List<TransmissionOrganDictDTO> data);

    @Mapping(target = "diagnosisCode", source = "value")
    @Mapping(target = "diagnosisName", source = "label")
    @Mapping(target = "showName", source = "label")
    @Mapping(target = "status", source = "status")
    InquiryDiagnosisRespVO convertDictDtoRespVo(TransmissionOrganDictDTO dictDTO);


    @Mapping(target = "showName", source = "diagnosisName")
    InquiryDiagnosisRespVO convertSearchVo(InquiryDiagnosticsSearchRespDto data);

    List<InquiryDiagnosisDto> convertDos(List<InquiryDiagnosisRespVO> inquiryDiagnosisRespVOS);

    InquiryDiagnosisPageReqVO convertPageDto(InquiryDiagnosisDto pageReqDto);

    List<InquiryDiagnosisDO> convertDictDtos(List<TransmissionOrganDictDTO> list);


    @Mapping(target = "diagnosisCode", source = "value")
    @Mapping(target = "diagnosisName", source = "label")
    @Mapping(target = "showName", source = "label")
    @Mapping(target = "status", source = "status")
    InquiryDiagnosisDO convertDictDtos(TransmissionOrganDictDTO dictDTO);


    default TransmissionOrganDictDTO convertDictQueryDto(InquiryDiagnosisPageReqVO pageReqVO) {
        return TransmissionOrganDictDTO.builder()
            .dictType(DictTypeConstants.DIAGNOSIS_DICT) // 诊断字典类型
            .label(pageReqVO.getDiagnosisName()) // 使用诊断名称作为标签查询条件
            .value(pageReqVO.getDiagnosisCode()) // 使用诊断编码作为值查询条件
            .status(pageReqVO.getStatus()) // 状态
            .outerValue(pageReqVO.getDiagnosisType() == null ? "" : pageReqVO.getDiagnosisType().toString()) // 类型
            .pageNo(pageReqVO.getPageNo())
            .pageSize(pageReqVO.getPageSize())
            .build();
    }

    List<InquiryDiagnosisRespVO> convertSearchRespVo(List<InquiryDiagnosisSearchRespDto> searchRespDtos);
}
