package com.xyy.saas.inquiry.hospital.server.service.diagnosis;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DIAGNOSIS_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DIAGNOSIS_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.common.api.rational.dto.ProductDiagnosisRelationApi;
import com.xyy.saas.inquiry.common.api.rational.dto.ProductDiagnosisRelationSearchDto;
import com.xyy.saas.inquiry.enums.system.DictDataTypeEnum;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisExcelVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.diagnosis.InquiryDiagnosisConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.diagnosis.InquiryDiagnosisMapper;
import com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.pojo.diagnosis.InquiryDiagnosisSearchRespDto;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import com.xyy.saas.inquiry.product.api.search.ProductSearchApi;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchReqDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryProductSearchReqDto;
import com.xyy.saas.transmitter.api.dict.TransmissionOrganDictApi;
import com.xyy.saas.transmitter.api.dict.dto.TransmissionOrganDictDTO;
import com.xyy.saas.transmitter.api.transmission.TransmissionConfigApi;
import com.xyy.saas.transmitter.enums.DictTypeConstants;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 问诊诊断信息 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class InquiryDiagnosisServiceImpl implements InquiryDiagnosisService {

    @Resource
    private InquiryDiagnosisMapper inquiryDiagnosisMapper;

    @DubboReference
    private ProductSearchApi productSearchApi;

    @DubboReference
    private TransmissionConfigApi transmissionConfigApi;

    @DubboReference
    private TransmissionOrganDictApi transmissionOrganDictApi;

    @DubboReference
    private ProductDiagnosisRelationApi productDiagnosisRelationApi;

    @Value("${inquiry.product.recommend.diagnosis.switch:false}")
    private boolean selfProductRecommendDiagnosisSwitch;

    @Override
    public Long createInquiryDiagnosis(InquiryDiagnosisSaveReqVO createReqVO) {
        // 校验数据重复
        validateDiagnosisCode(null, createReqVO.getDiagnosisCode());
        validateDiagnosisName(null, createReqVO.getDiagnosisName());
        InquiryDiagnosisDO inquiryDiagnosis = InquiryDiagnosisConvert.INSTANCE.convertDo(createReqVO);
        inquiryDiagnosisMapper.insert(inquiryDiagnosis);
        return inquiryDiagnosis.getId();
    }

    @Override
    public void updateInquiryDiagnosis(InquiryDiagnosisSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryDiagnosisExists(updateReqVO.getId());
        validateDiagnosisCode(updateReqVO.getId(), updateReqVO.getDiagnosisCode());
        validateDiagnosisName(updateReqVO.getId(), updateReqVO.getDiagnosisName());
        InquiryDiagnosisDO updateObj = InquiryDiagnosisConvert.INSTANCE.convertDo(updateReqVO);
        inquiryDiagnosisMapper.updateById(updateObj);
    }


    private void validateDiagnosisCode(Long id, String diagnosisCode) {
        if (StringUtils.isBlank(diagnosisCode)) {
            return;
        }
        List<InquiryDiagnosisDO> diagnosisCodes = inquiryDiagnosisMapper.queryByCondition(InquiryDiagnosisDto.builder().diagnosisCode(diagnosisCode).build());
        if (CollUtil.isEmpty(diagnosisCodes)) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的数据
        if (id == null) {
            throw exception(INQUIRY_DIAGNOSIS_EXISTS, diagnosisCode);
        }
        if (diagnosisCodes.stream().anyMatch(a -> !Objects.equals(a.getId(), id))) {
            throw exception(INQUIRY_DIAGNOSIS_EXISTS, diagnosisCode);
        }
    }

    private void validateDiagnosisName(Long id, String diagnosisName) {
        if (StringUtils.isBlank(diagnosisName)) {
            return;
        }
        List<InquiryDiagnosisDO> diagnosisNames = inquiryDiagnosisMapper.queryByCondition(InquiryDiagnosisDto.builder().diagnosisName(diagnosisName).build());
        if (CollUtil.isEmpty(diagnosisNames)) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的数据
        if (id == null) {
            throw exception(INQUIRY_DIAGNOSIS_EXISTS, diagnosisName);
        }
        if (diagnosisNames.stream().anyMatch(a -> !Objects.equals(a.getId(), id))) {
            throw exception(INQUIRY_DIAGNOSIS_EXISTS, diagnosisName);
        }
    }


    @Override
    public void deleteInquiryDiagnosis(Long id) {
        // 校验存在
        validateInquiryDiagnosisExists(id);
        // 删除
        inquiryDiagnosisMapper.deleteById(id);
    }

    private InquiryDiagnosisDO validateInquiryDiagnosisExists(Long id) {
        InquiryDiagnosisDO inquiryDiagnosisDO = inquiryDiagnosisMapper.selectById(id);
        if (inquiryDiagnosisDO == null) {
            throw exception(INQUIRY_DIAGNOSIS_NOT_EXISTS);
        }
        return inquiryDiagnosisDO;
    }

    @Override
    public InquiryDiagnosisDO getInquiryDiagnosis(Long id) {
        return inquiryDiagnosisMapper.selectById(id);
    }

    @Override
    public PageResult<InquiryDiagnosisDO> getInquiryDiagnosisPage(InquiryDiagnosisPageReqVO pageReqVO) {
        // 走诊断匹配的数据源
        Integer organId = transmissionConfigApi.diagnosisChangeQueryCatalog(pageReqVO.getTenantId(), pageReqVO.getPrescriptionType());
        if (organId != null) {
            TransmissionOrganDictDTO queryDTO = InquiryDiagnosisConvert.INSTANCE.convertDictQueryDto(pageReqVO).setOrganId(organId);
            // 调用外部服务查询
            PageResult<TransmissionOrganDictDTO> dictPage = transmissionOrganDictApi.queryDictPage(queryDTO);
            if (dictPage == null || CollUtil.isEmpty(dictPage.getList())) {
                return PageResult.empty();
            }
            return new PageResult(InquiryDiagnosisConvert.INSTANCE.convertDictDtos(dictPage.getList()), dictPage.getTotal());
        }
        // 默认走本地数据源
        return inquiryDiagnosisMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<InquiryDiagnosisDO> getDiagnosisPage(InquiryDiagnosisPageReqVO pageReqVO) {
        return inquiryDiagnosisMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InquiryDiagnosisRespVO> queryInquiryDiagnosis(InquiryDiagnosisDto diagnosisDto) {
        return InquiryDiagnosisConvert.INSTANCE.convertVos(inquiryDiagnosisMapper.queryByCondition(diagnosisDto));
    }

    public List<InquiryDiagnosisRespVO> recommendDiagnosis(InquiryDiagnosticsSearchReqDto reqDto) {
        if (!selfProductRecommendDiagnosisSwitch) {
            CommonResult<List<InquiryDiagnosisSearchRespDto>> result = productSearchApi.productDiagnosis(reqDto);
            if (result.isSuccess() && CollUtil.isNotEmpty(result.getData())) {
                // 过滤存在的诊断+类型
                List<InquiryDiagnosisRespVO> inquiryDiagnosisRespVOS = filterExistDiagnosis(result.getData(), reqDto);
                // 过滤三方诊断
                return getInquiryDiagnosisRespVOS(reqDto, inquiryDiagnosisRespVOS);
            }
            return new ArrayList<>();
        }

        List<String> productNames = Optional.ofNullable(reqDto.getProductSearchList()).orElse(List.of()).stream().map(InquiryProductSearchReqDto::getProductName).toList();

        List<InquiryDiagnosisSearchRespDto> searchRespDtos = productDiagnosisRelationApi.productDiagnostics(new ProductDiagnosisRelationSearchDto().setProductNames(productNames));

        List<InquiryDiagnosisRespVO> list = InquiryDiagnosisConvert.INSTANCE.convertSearchRespVo(searchRespDtos);

        // 过滤三方诊断
        return getInquiryDiagnosisRespVOS(reqDto, list);
    }


    private List<InquiryDiagnosisRespVO> filterExistDiagnosis(List<InquiryDiagnosisSearchRespDto> searchRespDtos, InquiryDiagnosticsSearchReqDto reqDto) {

        Integer medicineType = reqDto.getProductSearchList().getFirst().getMedicineType();

        List<String> diagnosisCodes = CollectionUtils.convertList(searchRespDtos, InquiryDiagnosisSearchRespDto::getDiagnosisCode);
        if (CollUtil.isEmpty(diagnosisCodes)) {
            return new ArrayList<>();
        }
        return queryInquiryDiagnosis(InquiryDiagnosisDto.builder()
            .diagnosisCodes(diagnosisCodes)
            .diagnosisType(medicineType)
            .status(CommonStatusEnum.ENABLE.getStatus()).build());
    }

    private List<InquiryDiagnosisRespVO> getInquiryDiagnosisRespVOS(InquiryDiagnosticsSearchReqDto reqDto, List<InquiryDiagnosisRespVO> inquiryDiagnosisRespVOS) {
        // 走诊断匹配的数据源
        Integer organId = transmissionConfigApi.diagnosisChangeQueryCatalog(reqDto.getTenantId(), reqDto.getPrescriptionType());

        if (organId == null) {
            return inquiryDiagnosisRespVOS;
        }
        List<Long> dictIds = CollectionUtils.convertList(inquiryDiagnosisRespVOS, InquiryDiagnosisRespVO::getId);

        if (CollUtil.isEmpty(dictIds)) {
            return new ArrayList<>();
        }
        log.info("查询诊断匹配的药品，organId：{}，诊断ids：{}", organId, dictIds);
        // 查询存在的
        TransmissionOrganDictDTO dictDTO = TransmissionOrganDictDTO.builder().organId(organId)
            .dictType(DictTypeConstants.DIAGNOSIS_DICT)
            .dictIds(dictIds).status(CommonStatusEnum.ENABLE.getStatus()).build();

        List<TransmissionOrganDictDTO> organDictDTOS = transmissionOrganDictApi.queryMatchSaasDictByDictIds(dictDTO);

        return InquiryDiagnosisConvert.INSTANCE.convertDictDtoRespVos(organDictDTOS);
    }


    @Override
    @Cacheable(cacheNames = RedisKeyConstants.PRESCRIPTION_RECOMMEND_DIAGNOSIS_KEY + "_com#10s")
    public List<InquiryDiagnosisDO> commonDiagnosis(InquiryDiagnosisPageReqVO pageReqVO) {
        // 走诊断匹配的数据源
        if (transmissionConfigApi.diagnosisChangeQueryCatalog(pageReqVO.getTenantId(), pageReqVO.getPrescriptionType()) != null) {
            return new ArrayList<>();
        }
        pageReqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        pageReqVO.setDataType(DictDataTypeEnum.SYSTEM.getCode());
        return inquiryDiagnosisMapper.queryByCondition(InquiryDiagnosisConvert.INSTANCE.convertPageDto(pageReqVO));
    }


    @Override
    public List<InquiryDiagnosisRespVO> queryInquiryDiagnosisByShowName(String showName, Integer status) {
        return InquiryDiagnosisConvert.INSTANCE.convertVos(inquiryDiagnosisMapper.queryInquiryDiagnosisByShowName(showName, status));
    }

    @Override
    public ImportResultDto importDiagnosisList(List<InquiryDiagnosisExcelVO> list, Boolean updateSupport) {
        if (CollUtil.isEmpty(list)) {
            throw new RuntimeException("导入数据不能为空");
        }
        // 校验-转换saveVO
        List<InquiryDiagnosisSaveReqVO> dictSaveReqVOS = checkAndGetImportList(list);
        if (CollUtil.isEmpty(dictSaveReqVOS)) {
            return ImportResultDto.builder().totalCount((long) list.size()).failureCount((long) list.size()).fileName("诊断导入失败数据.xls").fileUrl("").build();
        }
        // 去重
        List<InquiryDiagnosisSaveReqVO> saveReqVOS = dictSaveReqVOS.stream().distinct().toList();
        InquiryDiagnosisDto dto = InquiryDiagnosisDto.builder().build();

        for (List<InquiryDiagnosisSaveReqVO> reqVOS : Lists.partition(saveReqVOS, 500)) {
            // dto.setDiagnosisCodes(CollectionUtils.convertList(reqVOS, InquiryDiagnosisSaveReqVO::getDiagnosisCode));

            dto.setShowNames(CollectionUtils.convertList(reqVOS, InquiryDiagnosisSaveReqVO::getShowName));

            Map<String, InquiryDiagnosisDO> existsDictMap = inquiryDiagnosisMapper.queryByCondition(dto).stream()
                .collect(Collectors.toMap(InquiryDiagnosisDO::getShowName, Function.identity(), (a, b) -> b));
            // 修改
            // List<InquiryDiagnosisDO> updateList = reqVOS.stream().filter(r -> existsDictMap.containsKey(r.getDiagnosisCode()))
            //     .map(r -> InquiryDiagnosisConvert.INSTANCE.convertDo(r).setId(existsDictMap.get(r.getDiagnosisCode()).getId())).toList();
            // if (BooleanUtil.isTrue(updateSupport) && CollUtil.isNotEmpty(updateList)) {
            //     inquiryDiagnosisMapper.updateBatch(updateList);
            // }
            // 新增
            List<InquiryDiagnosisDO> createList = reqVOS.stream().filter(r -> !existsDictMap.containsKey(r.getShowName()))
                .map(InquiryDiagnosisConvert.INSTANCE::convertDo).toList();
            if (CollUtil.isNotEmpty(createList)) {
                inquiryDiagnosisMapper.insertBatch(createList);
            }
        }

        List<InquiryDiagnosisExcelVO> failList = list.stream().filter(d -> StringUtils.isNotBlank(d.getErrMsg())).toList();
        if (CollUtil.isNotEmpty(failList)) {
            // 上传文件
        }
        return ImportResultDto.builder().totalCount((long) list.size()).successCount((long) (list.size() - failList.size())).failureCount((long) failList.size()).fileName("诊断导入失败数据.xls").fileUrl("").build();
    }

    private List<InquiryDiagnosisSaveReqVO> checkAndGetImportList(List<InquiryDiagnosisExcelVO> list) {
        List<InquiryDiagnosisSaveReqVO> realList = new ArrayList<>();
        Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
        for (InquiryDiagnosisExcelVO excelVO : list) {
            InquiryDiagnosisSaveReqVO saveReqVO = InquiryDiagnosisConvert.INSTANCE.convertExcelVO(excelVO);
            Set<ConstraintViolation<InquiryDiagnosisSaveReqVO>> validate = validator.validate(saveReqVO);
            String errMsg = Optional.ofNullable(validate).orElse(new HashSet<>()).stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(errMsg)) {
                excelVO.setErrMsg(errMsg);
                continue;
            }
            realList.add(saveReqVO);
        }
        return realList;
    }
}