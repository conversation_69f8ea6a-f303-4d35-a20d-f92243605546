package com.xyy.saas.inquiry.hospital.server.service.doctor.chain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: cxy
 * @DateTime: 2025/8/13 19:08
 * @Description: 问诊药品指定医生过滤
 **/
@Component
@Slf4j
public class InquiryProductDoctorFilterChain extends DoctorFilterChain {

    @DubboReference
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    @Autowired
    private TenantApi tenantApi;

    @DubboReference
    private InquiryApi inquiryApi;


    @Override
    @TraceNode(node = TraceNodeEnum.INQUIRY_PRODUCT_DOCTOR_FILTER_MEDICARE, prefLocation = "inquiryDto.pref")
    public void filter(InquiryRecordDto inquiryDto, List<String> doctorList) {
        TenantDto tenantDto = tenantApi.getTenant();
        // 查询区域门店配置
        InquiryOptionConfigRespDto optionConfigRespDto = inquiryOptionConfigApi.getInquiryOptionConfig(tenantDto, InquiryOptionTypeEnum.PROC_INQUIRY_PRODUCT_DOCTOR);
        // 判断药品过滤医生
        if (!BooleanUtil.isTrue(optionConfigRespDto.getProcInquiryProductDoctor())
            || CollUtil.isEmpty(optionConfigRespDto.getAreaInquiryProductNames())
            || CollUtil.isEmpty(optionConfigRespDto.getAreaInquiryDoctorPrefs())) {
            return;
        }

        InquiryRecordDetailDto inquiryRecordDetail = inquiryApi.getInquiryRecordDetail(inquiryDto.getPref());
        boolean filterDoctor = validProductCanFilterDoctor(optionConfigRespDto, inquiryRecordDetail);
        log.info("问诊药品指定医生过滤,filterDoctor:{},pref:{},doctorPref:{}", filterDoctor, inquiryDto.getPref(), optionConfigRespDto.getAreaInquiryDoctorPrefs());
        if (filterDoctor && CollUtil.isNotEmpty(doctorList)) {
            doctorList.retainAll(optionConfigRespDto.getAreaInquiryDoctorPrefs());     // 取交集
        }
    }

    private static boolean validProductCanFilterDoctor(InquiryOptionConfigRespDto optionConfigRespDto, InquiryRecordDetailDto inquiryRecordDetail) {

        if (inquiryRecordDetail == null || inquiryRecordDetail.getPreDrugDetail() == null
            || CollUtil.isEmpty(inquiryRecordDetail.getPreDrugDetail().getInquiryProductInfos())) {
            return false;
        }

        List<String> productNames = inquiryRecordDetail.getPreDrugDetail().getInquiryProductInfos().stream()
            .map(InquiryProductDetailDto::getCommonName).distinct().toList();
        if (CollUtil.isEmpty(productNames)) {
            return false;
        }
        // 问诊商品名称在配置范围内
        return CollUtil.containsAny(optionConfigRespDto.getAreaInquiryProductNames(), productNames);
    }
}
