package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import lombok.Data;
import java.io.Serializable;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/08/20 13:54
 */
@Data
public class TenantBindStoreRespVO implements Serializable {

    /**
     * 药店机构标识
     */
    private String organSign;
    /**
     * 药店简介名称
     */
    private String drugstoreName;

    /**
     * 存在绑定关系
     */
    private boolean existBind;

    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;
    /**
     * 药店地址
     */
    private String address;

}
