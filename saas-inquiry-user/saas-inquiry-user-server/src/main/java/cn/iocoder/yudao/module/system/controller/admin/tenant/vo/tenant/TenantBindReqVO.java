package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import cn.iocoder.yudao.framework.common.validation.Mobile;
import com.xyy.saas.inquiry.constant.ValidateGroup.Add;
import com.xyy.saas.inquiry.constant.ValidateGroup.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.io.Serializable;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/20 13:54
 */
@Data
public class TenantBindReqVO implements Serializable {

    @Schema(description = "门店id", example = "MD100001", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @Schema(description = "荷叶门店ID", example = "LZA00001", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "荷叶门店ID不能为空", groups = Add.class)
    @Length(max = 64, message = "荷叶门店ID最大长度为 {value}", groups = Add.class)
    private String hyOrganSign;

    @Schema(description = "是否换绑-后台使用", example = "true")
    private boolean reBind;


    @Schema(description = "手机号-门店绑定必填", example = "15926350000", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "手机号不能为空", groups = Update.class)
    @Mobile(groups = Update.class)
    private String mobile;


    @Schema(description = "短信验证码-门店绑定必填", requiredMode = Schema.RequiredMode.REQUIRED, example = "默认验证码:1111")
    private String code;

    @Schema(description = "选门店二次绑定确认", example = "true")
    private boolean confirm;
}
