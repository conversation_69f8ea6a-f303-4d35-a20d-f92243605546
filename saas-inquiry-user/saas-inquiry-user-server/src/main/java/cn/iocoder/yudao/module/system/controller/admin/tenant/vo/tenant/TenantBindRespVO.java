package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/20 13:54
 */
@Data
@Accessors(chain = true)
public class TenantBindRespVO implements Serializable {

    @Schema(description = "门店列表")
    private List<TenantBindStoreRespVO> bindList;

    @Schema(description = "门店是否绑定成功", example = "true")
    private boolean bind;
}
