package com.xyy.saas.inquiry.enums.rational;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

public enum WomenEnums {
    WOMEN_ENCYESIS(1,"妊娠期"),

    WOMEN_LACTATION(2,"哺乳期"),

    WOMEN_ALL(3,"妊娠哺乳期");



    private  Integer code;
    private  String name;

    WomenEnums(Integer code, String name){
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static WomenEnums getWomenEnumsByCode(Integer code){
        for(WomenEnums item : WomenEnums.values()){
            if(item.code.equals(code)){
                return item;
            }
        }
        return null;
    }

    public static String getWomenNameByCode(Integer code){
        for(WomenEnums item : WomenEnums.values()){
            if(item.code.equals(code)){
                return item.name;
            }
        }
        return null;
    }

    public static List<String> nameList(){
        return Arrays.stream(values()).map(WomenEnums::getName).collect(Collectors.toList());
    }

    public static WomenEnums valueOfName(String s) {
        for(WomenEnums item : WomenEnums.values()){
            if(StringUtils.equals(s,item.getName())){
                return item;
            }
        }
        return null;
    }

}
