package com.xyy.saas.inquiry.enums.rational;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 合理用药 字段枚举
 */
public enum RationalStatusEnum {

    ON( 0, "启用" ),

    OFF( 1, "禁用" );

    private Integer code;

    private String name;

    RationalStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static RationalStatusEnum valueOfName(String name){
        if(StringUtils.isBlank(name)){
            return null;
        }
        for (RationalStatusEnum value : values()) {
            if (StringUtils.equals(name,value.name)) {
                return value;
            }
        }
        return null;
    }
    
    public static RationalStatusEnum typeOfCode(Integer code){
        if(code == null){
            return null;
        }
        for (RationalStatusEnum value : values()) {
            if (Objects.equals(code,value.code)) {
                return value;
            }
        }
        return null;
    }
    
}
