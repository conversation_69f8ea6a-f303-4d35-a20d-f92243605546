package com.xyy.saas.inquiry.enums.rational;

/**
 * <AUTHOR>
 * @description:
 * @date 2022-05-12 02:04:24
 */
public enum IndexAliasEnum {
    INQUIRY_INFO(1,"hewz_inquiry_report_read"),
    PRECRIPTION(2,"hewz_inquiry_prescription_read"),
    PRECRIPTION_DETAIL(3,"hewz_prescription_detail_read"),
    PATIENT_INFO(4,"hewz_patient_info_read"),
    DRUGSTORE_INFO(5,"hewz_drugstore_info_read"),
    INQUIRY_PRESCRIPTION_AGG(6,"hywz_inquiry_prescription_agg_read"),
    PRESCRIPTION_SNAPSHOT_AGG(7,"ykq_remote_prescription_snapshot_agg_read"),

    YKQ_REMOTE_PRODUCT_RULE(8,"ykq_remote_product_rule_aliases"),

    INQUIRY_INFO_NEW(9,"hewz_inquiry_report_read_new"),
    INQUIRY_PRESCRIPTION_AGG_NEW(10,"hywz_inquiry_prescription_agg_read_new"),
    PRESCRIPTION_SNAPSHOT_AGG_NEW(11,"ykq_remote_prescription_snapshot_agg_read_new"),
    PRECRIPTION_DETAIL_NEW(3,"hewz_prescription_detail_read_new");

    private int id;
    private String readAlias;

    IndexAliasEnum(int id, String readAlias) {
        this.id = id;
        this.readAlias = readAlias;
    }

    public int getId() {
        return id;
    }

    public String getReadAlias() {
        return readAlias;
    }

}
