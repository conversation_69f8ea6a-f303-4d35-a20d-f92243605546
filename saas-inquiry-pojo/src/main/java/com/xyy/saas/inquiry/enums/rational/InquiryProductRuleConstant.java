package com.xyy.saas.inquiry.enums.rational;

public class InquiryProductRuleConstant {
    /**
     * 全部
     */
    public final static Integer ALL  = -1;

    /**
     * 1类别
     */
    public final static Integer FIRST_TYPE_CATEGORY = 1;


    /**
     * 2名称
     */
    public final static Integer FIRST_TYPE_COMMON_NAME = 2;






    /**
     * 未设置
     */
    public final static Integer NOT_SET  = -1;


    /**
     * 匹配全部
     */
    public final static Integer MATCH_ALL  = 1;

    /**
     * 匹配任一
     */
    public final static Integer MATCH_ANY  = 0;
}
