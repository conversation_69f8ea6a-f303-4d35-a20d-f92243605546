package com.xyy.saas.inquiry.enums.rational;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Desc 警示级别
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/11/06 14:18
 */
public enum CautionLevel {

    unlimited(0,"不限"),

    remind(1,"提醒"),

    cautiously(2,"慎用"),

    contraindicated(3,"忌用"),

    forbidden(4,"禁用");

    @Getter
    private final Integer type;
    @Getter
    private final String tipsName;

    CautionLevel(Integer type, String tipsName) {
        this.type = type;
        this.tipsName = tipsName;
    }

    public static CautionLevel valueOf(byte index) {
        for (CautionLevel warningLevel : CautionLevel.values()) {
            if (index == warningLevel.getType()) {
                return warningLevel;
            }
        }
        return null;
    }

    public static CautionLevel valueOfName(String name) {
        for (CautionLevel warningLevel : CautionLevel.values()) {
            if (StringUtils.equals(name,warningLevel.getTipsName())) {
                return warningLevel;
            }
        }
        return CautionLevel.unlimited;
    }


    public static String getTipsNameByType(Integer type) {
        for (CautionLevel warningLevel : CautionLevel.values()) {
            if (type == warningLevel.getType()) {
                return warningLevel.tipsName;
            }
        }
        return null;
    }


}
