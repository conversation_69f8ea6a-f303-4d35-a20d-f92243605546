package com.xyy.saas.inquiry.enums.rational;

/**
 * 合理用药字典配置枚举
 */
public enum RationalDictType {

    AGE_GROUP( 1, "年龄段" ),

    COMPONENT( 2, "成分" ),

    CATEGORY( 3, "类别" );


    private Integer type;

    private String desc;

    RationalDictType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
