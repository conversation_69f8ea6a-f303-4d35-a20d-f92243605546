package com.xyy.saas.inquiry.enums.rational;

public enum LimitTypeEnum {
    SEX_LIMIT(1,"性别类限制"),

    AGE_LIMIT(2,"年龄分类限制"),

    AGE_RANGE_LIMIT(3,"年龄区间限制"),

    HEALTH_LIMIT(4,"肝肾功能限制"),

    WOMEN_LIMIT(5,"妊娠哺乳期限制"),


    INGREDIENT_LIMIT(6,"成分过敏限制");

    private Integer type;

    private String desc;

    public Integer getType() {
        return type;
    }


    public String getDesc() {
        return desc;
    }


    LimitTypeEnum(Integer type , String desc){
        this.type = type;
        this.desc = desc;
    }
}
