package com.xyy.saas.inquiry.enums.rational;

public enum InquiryProductRuleFieldEnum {
    ID("id"),
    PRODUCT_COMMON_ID("productCommonId"),
    COMMON_NAME("commonName"),
    CATEGORY_LV6_ID("categoryLv6Id"),
    MEDICARE_REMARK("medicareRemark"),
    LIMIT_DRUG_TYPE("limitDrugType"),
    PARAM("param"),
    CAUTION("caution"),
    RANGE_BEGIN("rangeBegin"),
    RANGE_END("rangeEnd"),
    STATUS("status"),
    YN("yn"),
    CREATE_USER("createUser"),
    UPDATE_USER("updateUser"),

    CREATE_TIME("createTime"),
    UPDATE_TIME("updateTime");

    private String fieldName;

    InquiryProductRuleFieldEnum(String fieldName){
        this.fieldName = fieldName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }
}
