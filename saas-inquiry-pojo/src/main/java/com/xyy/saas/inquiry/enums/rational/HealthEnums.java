package com.xyy.saas.inquiry.enums.rational;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

public enum HealthEnums {
    HEALTH_HEPAR_FAIL(1,"肝功能异常"),

    HEALTH_KIDNEY_FAIL(2,"肾功能异常"),

    HEALTH_ALL_FAIL(3,"肝肾功能异常");



    private  Integer code;
    private  String name;

    HealthEnums(Integer code,String name){
        this.code = code;
        this.name = name;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static HealthEnums getHealthEnumsByCode(Integer code){
        for(HealthEnums item : HealthEnums.values()){
            if(item.code.equals(code)){
                return item;
            }
        }
        return null;
    }

    public static String getHealthNameByCode(Integer code){
        for(HealthEnums item : HealthEnums.values()){
            if(item.code.equals(code)){
                return item.getName();
            }
        }
        return null;
    }

    public static List<String> nameList(){
        return Arrays.stream(values()).map(HealthEnums::getName).collect(Collectors.toList());
    }


    public static HealthEnums valueOfName(String name) {
        for (HealthEnums healthEnums : values()) {
            if(StringUtils.equals(name,healthEnums.getName())){
                return healthEnums;
            }
        }
        return null;


    }
}
