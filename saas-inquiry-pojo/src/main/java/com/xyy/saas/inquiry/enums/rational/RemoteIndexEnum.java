package com.xyy.saas.inquiry.enums.rational;


/**
 * @author: hanmo
 * @Date: 2021/03/08
 */
public enum RemoteIndexEnum {
    /**
     * 荷叶处方索引
     */
    YKQ_REMOTE_PRESCRIPTION(0, "ykq_remote_prescription", "ykq_remote_prescription_", "_doc", "荷叶处方索引", "saas_inquiry_prescription"),

    YKQ_REMOTE_REPORT(1,"ykq_remote_report","ykq_remote_report_","_doc","荷叶问诊索引","saas_inquiry_report"),

    YKQ_REMOTE_PATIENT_INFO(2, "ykq_remote_patient_info", "ykq_remote_patient_info_","_doc","荷叶患者索引","saas_user_patient_info"),

    /* YKQ_FULL_UPDATE_NEW_INDEX(3, ConfigService.getAppConfig().getProperty("ES.fullupdate.handle.index.name",""),ConfigService.getAppConfig().getProperty("ES.fullupdate.handle.index.name","")+"_","_doc","荷叶全量刷新索引临时索引",ConfigService.getAppConfig().getProperty("ES.fullupdate.handle.index.name","")), */

    YKQ_REMOTE_PRODUCT_RULE(4, "ykq_remote_product_rule", "ykq_remote_product_rule", "_doc", "荷叶问诊合理用药药品规则配置", "inquiry_rational_product_rule"),

    YKQ_REMOTE_DIAGNOSISC(5, "ykq_remote_diagnosisc", "ykq_remote_diagnosisc", "_doc", "荷叶问诊合理用药诊断禁忌配置", "inquiry_rational_diagnosis"),
    YKQ_REMOTE_COMPATIBILITY(6, "ykq_remote_compatibility", "ykq_remote_compatibility", "_doc", "荷叶问诊合理用药配伍禁忌配置", "inquiry_rational_compatibility_detail"),
    YKQ_REMOTE_ANTIBACTERIAL_DRUG(7, "ykq_remote_antibacterial_drug", "ykq_remote_antibacterial_drug", "_doc", "荷叶问诊抗菌药品配置", "saas_inquiry_antibacterial_drug_limit");

    /**
     * 索引编码
     */
    private Integer code;
    /*
     * 索引名
     */
    private String indexName;
    /**
     * 索引前缀
     */
    private String indexPrefix;
    /**
     * 索引类型
     */
    private String indexType;
    /**
     * 备注
     */
    private String remark;

    /**
     * 表名
     */
    private String tableName;

    RemoteIndexEnum(Integer code, String indexName, String indexPrefix, String indexType, String remark, String tableName) {
        this.code = code;
        this.indexName = indexName;
        this.indexPrefix = indexPrefix;
        this.indexType = indexType;
        this.remark = remark;
        this.tableName = tableName;
    }

    public Integer getCode() {
        return code;
    }

    public String getIndexName() {
        return indexName;
    }

    public String getIndexPrefix() {
        return indexPrefix;
    }

    public String getIndexType() {
        return indexType;
    }

    public String getRemark() {
        return remark;
    }

    public String getTableName() {
        return tableName;
    }
}
