package com.xyy.saas.inquiry.enums.rational;

/**
 * 合理用药 字段枚举
 */
public enum RationalFieldType {

    CATEGORY( 1, "类别" ),

    COMMON_NAME( 2, "通用名" );


    private Integer type;

    private String desc;

    RationalFieldType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
