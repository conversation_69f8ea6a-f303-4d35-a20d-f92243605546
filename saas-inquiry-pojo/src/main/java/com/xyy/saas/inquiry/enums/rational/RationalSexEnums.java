package com.xyy.saas.inquiry.enums.rational;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

public enum RationalSexEnums {

    SEX_UNLIMITED(0,"","不限"),

    SEX_MAN(1,"男","男性用药"),

    SEX_WOMAN(2,"女","女性用药");

    private  Integer code;
    private  String name;
    private  String desc;

    RationalSexEnums(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }


    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public static List<String> descList(){
        return Arrays.stream(values()).map(RationalSexEnums::getDesc).collect(Collectors.toList());
    }

    public static String getDescByCode(Integer code){
        for(RationalSexEnums sexEnums :values()){
            if(sexEnums.getCode().equals(code)){
                return sexEnums.getDesc();
            }
        }
        return null;
    }

    public static String getNameByCode(Integer code){
        for(RationalSexEnums sexEnums :values()){
            if(sexEnums.getCode().equals(code)){
                return sexEnums.getName();
            }
        }
        return "";
    }

    public static RationalSexEnums valueOfDesc(String name){
        for (RationalSexEnums sexEnums : values()) {
            if(StringUtils.equals(name,sexEnums.getDesc())){
                return sexEnums;
            }
        }
        return null;
    }
}
