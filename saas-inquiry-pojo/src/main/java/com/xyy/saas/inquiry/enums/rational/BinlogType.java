package com.xyy.saas.inquiry.enums.rational;

import com.google.common.collect.Sets;
import java.util.Set;
import lombok.Getter;

/**
 * @author: hanmo
 * @Date: 2021/03/08
 */
@Getter
public enum BinlogType {
    /**
     * 新增记录
     */
    INSERT(0, "INSERT", "新增记录"),
    /**
     * 更新记录
     */
    UPDATE(1, "UPDATE", "更新记录"),
    /**
     * 删除记录
     */
    DELETE(2, "DELETE", "删除记录"),
    ;

    private int code;
    private String type;
    private String remark;

    BinlogType(int code, String type, String remark) {
        this.code = code;
        this.type = type;
        this.remark = remark;
    }

    public static final Set<String> TYPE_SET = Sets.newHashSet(
            INSERT.type,
            UPDATE.type
    );
}
