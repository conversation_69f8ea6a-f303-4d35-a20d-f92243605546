package com.xyy.saas.inquiry.trace.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 链路追踪节点数据
 * 用于存储业务流程中各个节点的执行信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceNodeData {

    /**
     * 主键ID
     */
    private String id;

    /**
     * 业务单据号，如问诊单号
     */
    private String businessNo;

    /**
     * 节点编码
     */
    private String nodeCode;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 节点说明
     */
    private String nodeDesc;

    /**
     * 开始执行时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行耗时(毫秒)
     */
    private Long duration;

    /**
     * 执行结果，true-成功，false-失败
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 业务数据，JSON格式
     */
    private String businessData;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 环境标识
     */
    private String envTag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}