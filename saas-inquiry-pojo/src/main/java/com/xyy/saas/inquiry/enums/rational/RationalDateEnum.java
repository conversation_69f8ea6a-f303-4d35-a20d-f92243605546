package com.xyy.saas.inquiry.enums.rational;

import java.math.BigDecimal;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

/**
 * 合理用药 日期配置枚举
 */
public enum RationalDateEnum {

    DAY( 1, "天" ,1){
        @Override
        public Integer getDay(Integer param) {
            return Optional.ofNullable(param).orElse(0);
        }

        @Override
        public Integer getDay(BigDecimal param) {
            return param.intValue();
        }
    },

    WEEK( 2, "周" ,7) {
        @Override
        public Integer getDay(Integer param) {
            if(param == null){
                return 0;
            }
            return param*7;
        }

        @Override
        public Integer getDay(BigDecimal param) {
            if(param == null){
                return 0;
            }
            return param.multiply(new BigDecimal("7")).intValue();
        }
    },

    MONTH( 3, "月" ,30) {
        @Override
        public Integer getDay(Integer param) {
            if(param == null){
                return 0;
            }
            return param*30;
        }

        @Override
        public Integer getDay(BigDecimal param) {
            if(param == null){
                return 0;
            }
            return param.multiply(new BigDecimal("30")).intValue();
        }
    },

    YEAR( 4, "岁" ,365) {
        @Override
        public Integer getDay(Integer param) {
            if(param == null){
                return 0;
            }
            return param*365;
        }

        @Override
        public Integer getDay(BigDecimal param) {
            if(param == null){
                return 0;
            }
            return param.multiply(new BigDecimal("365")).intValue();
        }
    };

    private final Integer index;

    private final String name;

    private final Integer value;

    public abstract Integer getDay(Integer param);

    public abstract Integer getDay(BigDecimal param);

    RationalDateEnum(Integer index, String name, Integer value) {
        this.index = index;
        this.name = name;
        this.value = value;
    }

    public Integer getIndex() {
        return index;
    }

    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }

    public static RationalDateEnum containsOfName(String name){
        if(StringUtils.isBlank(name)){
            return null;
        }
        for (RationalDateEnum dateEnum : values()) {
            if(StringUtils.contains(name,dateEnum.getName())){
                return dateEnum;
            }
        }
        return null;
    }


}
