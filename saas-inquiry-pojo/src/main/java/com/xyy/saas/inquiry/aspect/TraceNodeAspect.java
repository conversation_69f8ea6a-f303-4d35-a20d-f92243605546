package com.xyy.saas.inquiry.aspect;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.trace.model.TraceNodeData;
import com.xyy.saas.inquiry.trace.parser.DefaultTraceDataParser;
import com.xyy.saas.inquiry.trace.parser.TraceDataParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.BeansException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import java.time.Instant;
import java.time.ZoneId;

import static cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder.getTenantContextInfo;

/**
 * 链路追踪切面 用于拦截带有TraceNode注解的方法，记录方法执行的开始时间、结束时间、执行结果等信息
 */
@Aspect
@Component
@Order(1)
@ConditionalOnProperty(name = {"spring.application.name"},  havingValue= "inquiry-kernel-all")
@Slf4j
public class TraceNodeAspect implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Around("@annotation(com.xyy.saas.inquiry.annotation.TraceNode)")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        // 获取注解
        TraceNode traceNode = method.getAnnotation(TraceNode.class);
        if (traceNode == null) {
            return point.proceed();
        }

        // 记录开始时间（截断到毫秒精度）
        LocalDateTime startTime = truncateToMillis(LocalDateTime.now());
        Object result = null;
        Throwable businessException = null;

        // 执行业务代码
        try {
            result = point.proceed();
        } catch (Throwable e) {
            businessException = e;
            throw e; // 重新抛出业务异常，确保业务代码的异常能够正常传播
        } finally {
            // 在finally块中处理埋点逻辑，确保无论业务代码是否异常都会执行
            try {
                handleTraceNode(point, method, traceNode, startTime, result, businessException);
            } catch (Exception e) {
                // 埋点逻辑的异常不应该影响业务代码，只记录日志
                log.error("链路追踪埋点失败，但不影响业务执行", e);
            }
        }

        return result;
    }

    /**
     * 处理链路追踪埋点逻辑
     */
    private void handleTraceNode(ProceedingJoinPoint point, Method method, TraceNode traceNode,
        LocalDateTime startTime, Object result, Throwable businessException) {
        // 获取节点枚举
        TraceNodeEnum nodeEnum = traceNode.node();
        // 获取业务单号位置
        String prefLocation = traceNode.prefLocation();

        // 创建链路追踪数据
        TraceNodeData traceNodeData = createTraceNodeData(nodeEnum);
        LocalDateTime endTime = truncateToMillis(LocalDateTime.now());

        try {
            // 获取数据解析器
            TraceDataParser parser = getParser(nodeEnum.getParser());
            // 解析业务单据号
            String businessNo = parser.parseBusinessNo(method, point.getArgs(), result, prefLocation);
            // 解析业务数据
            Object businessData = parser.parseBusinessData(method, point.getArgs(), result);

            // 设置相关参数
            traceNodeData.setBusinessNo(businessNo);
            traceNodeData.setBusinessData(JSON.toJSONString(businessData));
            traceNodeData.setStartTime(startTime);
            traceNodeData.setEndTime(endTime);
            traceNodeData.setDuration(ChronoUnit.MILLIS.between(startTime, endTime));
            traceNodeData.setSuccess(businessException == null);
            if (businessException != null) {
                traceNodeData.setErrorMsg(businessException.getMessage());
            }

            // 保存链路追踪数据
            saveTraceNodeData(traceNodeData, traceNode.async());
        } catch (Exception e) {
            log.error("处理链路追踪数据失败", e);
        }
    }

    /**
     * 获取数据解析器
     *
     * @param parserClassName 解析器类名
     * @return 数据解析器
     */
    private TraceDataParser getParser(String parserClassName) {
        // 获取默认的转化器
        TraceDataParser defaultParser = applicationContext.getBean(DefaultTraceDataParser.class);
        if (StringUtils.isBlank(parserClassName)) {
            return defaultParser;
        }

        try {
            Class<?> parserClass = ClassUtils.forName(parserClassName, ClassUtils.getDefaultClassLoader());
            if (!TraceDataParser.class.isAssignableFrom(parserClass)) {
                log.warn("指定的解析器类 {} 不是 TraceDataParser 的实现类，将使用默认解析器", parserClassName);
                return defaultParser;
            }

            // 尝试从Spring容器中获取
            try {
                return (TraceDataParser) applicationContext.getBean(parserClass);
            } catch (BeansException e) {
                // 如果Spring容器中不存在，则创建新实例
                return (TraceDataParser) ReflectionUtils.accessibleConstructor(parserClass).newInstance();
            }
        } catch (Exception e) {
            log.warn("创建解析器实例失败，将使用默认解析器", e);
            return defaultParser;
        }
    }

    /**
     * 创建链路追踪数据
     *
     * @param nodeEnum     节点枚举
     * @return 链路追踪数据
     */
    private TraceNodeData createTraceNodeData(TraceNodeEnum nodeEnum) {
        TraceNodeData traceNodeData = new TraceNodeData();
        traceNodeData.setId(UUID.randomUUID().toString().replace("-", ""));
        traceNodeData.setNodeCode(nodeEnum.getCode());
        traceNodeData.setNodeName(nodeEnum.getName());
        traceNodeData.setCreateTime(truncateToMillis(LocalDateTime.now()));
        // 设置租户信息
        try {
            TenantDto tenantDto = getTenantContextInfo(TenantConstant.TENANT_CONTEXT_KEY_TENANT_DTO);
            if (tenantDto != null) {
                traceNodeData.setTenantId(tenantDto.getId());
                traceNodeData.setEnvTag(tenantDto.getEnvTag());
            }
        } catch (Exception e) {
            log.warn("获取租户信息失败", e);
        }

        return traceNodeData;
    }

    /**
     * 截断LocalDateTime到毫秒精度，避免纳秒精度导致Elasticsearch转换失败
     *
     * @param dateTime 原始时间
     * @return 截断到毫秒精度的时间
     */
    private LocalDateTime truncateToMillis(LocalDateTime dateTime) {
        Instant instant = dateTime.atZone(ZoneId.systemDefault()).toInstant();
        long millis = instant.toEpochMilli();
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(millis), ZoneId.systemDefault());
    }

    /**
     * 保存链路追踪数据
     * 🔥 优化版：优先使用缓冲服务，提升吞吐量，避免CPU飙升
     *
     * @param traceNodeData 链路追踪数据
     * @param async         是否异步保存
     */
    private void saveTraceNodeData(TraceNodeData traceNodeData, boolean async) {
        try {
            // 🔥 优先使用缓冲服务（批量写入ES，提升吞吐量）
            try {
                Object bufferService = applicationContext.getBean("traceBufferPoolService");
                if (bufferService != null && async) {
                    // 调用缓冲版异步保存（写入缓冲队列即返回）
                    bufferService.getClass()
                        .getMethod("save", TraceNodeData.class)
                        .invoke(bufferService, traceNodeData);
                    return; // 成功使用缓冲服务，直接返回
                }
            } catch (Exception e) {
                log.warn("缓冲链路节点保存失败", e);
            }
        } catch (Exception e) {
            log.warn("链路追踪数据保存失败（已静默处理，不影响主业务）", e);
        }
    }
}
