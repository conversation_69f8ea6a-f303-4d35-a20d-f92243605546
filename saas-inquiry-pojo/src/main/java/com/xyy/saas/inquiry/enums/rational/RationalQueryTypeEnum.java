package com.xyy.saas.inquiry.enums.rational;

import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

/**
 * 合理用药 字段枚举
 */
public enum RationalQueryTypeEnum {

    UNKNOW( null, "全部" ),
    UNSET( -1, "未设置" ),
    ANY_MATCH( 0, "任一条件满足" ),
    ALL_MATCH( 1, "全部包含满足" );


    private final Integer code;

    private final String name;

    RationalQueryTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static RationalQueryTypeEnum valueOfName(String name){
        if(StringUtils.isBlank(name)){
            return null;
        }
        for (RationalQueryTypeEnum value : values()) {
            if (StringUtils.equals(name,value.name)) {
                return value;
            }
        }
        return null;
    }
    
    public static RationalQueryTypeEnum typeOfCode(Integer code){
        if(code == null){
            return null;
        }
        for (RationalQueryTypeEnum value : values()) {
            if (Objects.equals(code,value.code)) {
                return value;
            }
        }
        return null;
    }
    
}
