package com.xyy.saas.inquiry.common.dal.dataobject.migration;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 处方迁移失败记录表
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/20 21:30
 */
@TableName("saas_migration_prescription_record")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MigrationPrescriptionRecord implements Serializable {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 机构号
     */
    private String organSign;

    /**
     * 最小ID
     */
    private Long minId;

    /**
     * 最大ID
     */
    private Long maxId;

    /**
     * 迁移成功数量
     */
    private Integer count;

    /**
     * 处理状态 0-成功 1-MQ发送失败 2-ES存储失败
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
