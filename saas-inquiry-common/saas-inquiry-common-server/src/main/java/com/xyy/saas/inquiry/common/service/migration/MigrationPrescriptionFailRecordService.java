package com.xyy.saas.inquiry.common.service.migration;

import com.xyy.saas.inquiry.common.dal.dataobject.migration.MigrationPrescriptionRecord;

/**
 * 处方迁移失败记录服务接口
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/20 21:36
 */
public interface MigrationPrescriptionFailRecordService {

    /**
     * 保存失败记录
     *
     * @param failRecord 失败记录
     */
    void saveRecord(MigrationPrescriptionRecord failRecord);

    /**
     * 创建ES存储成功记录
     *
     * @param organSign 机构号
     * @param minId     最小ID
     * @param maxId     最大ID
     * @param count     数量
     */
    void createEsSuccessRecord(String organSign, Long minId, Long maxId, Integer count);

    /**
     * 创建MQ发送失败记录
     *
     * @param organSign  机构号
     * @param minId      最小ID
     * @param maxId      最大ID
     * @param failReason 失败原因
     */
    void createMqFailRecord(String organSign, Long minId, Long maxId, String failReason);

    /**
     * 创建ES存储失败记录
     *
     * @param organSign  机构号
     * @param minId      最小ID
     * @param maxId      最大ID
     * @param failReason 失败原因
     */
    void createEsFailRecord(String organSign, Long minId, Long maxId, String failReason);
}
