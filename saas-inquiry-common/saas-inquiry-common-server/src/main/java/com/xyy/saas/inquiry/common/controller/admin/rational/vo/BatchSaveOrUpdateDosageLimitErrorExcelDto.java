package com.xyy.saas.inquiry.common.controller.admin.rational.vo;

import com.xyy.saas.inquiry.common.annotation.excel.ExcelName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**
 * 批量新增或修改excel对象
 *
 * <AUTHOR>
 * @Date 4/22/24 10:39 AM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchSaveOrUpdateDosageLimitErrorExcelDto implements Serializable {

    private static final long serialVersionUID = 6302938940802062648L;

    @ExcelName(value = "中台商品ID", order = "1", filter = "non2Str")
    private String standardId;

    @ExcelName(value = "是否长处方药品", order = "2", filter = "non2Str")
    private String whetherLongPrescription;

    @ExcelName(value = "最小包装数量", order = "3", filter = "non2Str")
    private String minimumPackageQuantity;

    @ExcelName(value = "总使用剂量限制", order = "4", filter = "non2Str")
    private String totalDoseLimit;

    @ExcelName(value = "限制等级", order = "5", filter = "non2Str")
    private String cautionLevelDesc;

    @ExcelName(value = "失败原因", order = "6")
    private String errorDetail;

}
