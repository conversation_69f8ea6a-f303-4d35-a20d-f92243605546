package com.xyy.saas.inquiry.common.service.migration;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.xyy.saas.inquiry.common.constant.MigrationConstant;
import com.xyy.saas.inquiry.common.convert.migration.MigrationConvert;
import com.xyy.saas.inquiry.common.mq.message.migration.MigrationPrescriptionEvent;
import com.xyy.saas.inquiry.common.mq.message.migration.dto.MigrationPrescriptionEventDto;
import com.xyy.saas.inquiry.common.mq.producer.migration.MigrationPrescription2EsMQProducer;
import com.xyy.saas.inquiry.pharmacist.api.migration.PrescriptionMigrationApi;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import com.xyy.saas.inquiry.util.ThreadPoolManager;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.IndexedObjectInformation;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 处方迁移服务实现类
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/20 21:14
 */
@Slf4j
@Service
public class MigrationPrescriptionServiceImpl implements MigrationPrescriptionService {

    // Redis 键名常量
    private static final String MIGRATION_PRESCRIPTION_FLAG_KEY = "migration:prescription:flag:";
    private static final String MIGRATION_PRESCRIPTION_ES_PROGRESS_KEY = "migration:prescription:es:progress:";

    @Resource
    private PrescriptionMigrationApi prescriptionMigrationApi;

    @Resource
    protected StringRedisTemplate stringRedisTemplate;

    @Resource
    private ElasticsearchOperations elasticsearchOperations;

    @Resource
    private MigrationPrescription2EsMQProducer migrationPrescription2EsMQProducer;

    @Resource
    private ConfigApi configApi;

    @Resource
    private MigrationPrescriptionFailRecordService failRecordService;

    /**
     * 获取迁移延迟时间 默认1000ms
     */
    public Long getMigrationDelayTime() {
        return NumberUtils.toLong(configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_DELAY_TIME), 1000);
    }

    /**
     * 获取迁移分页大小 默认1000
     */
    private Integer getMigrationPageSize() {
        return NumberUtils.toInt(configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_PAGE_SIZE), 1000);
    }

    /**
     * 获取迁移开关 默认开启
     */
    private boolean getMigrationSwitch() {
        String byKey = configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_SWITCH);
        return StringUtils.equals(byKey, "1");
    }


    @Override
    public void migrationPrescription(MigrationPrescriptionReqDto reqDto) {
        log.info("开始处方迁移任务，参数：{}", JSONUtil.toJsonStr(reqDto));

        Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(MIGRATION_PRESCRIPTION_FLAG_KEY, "1");
        if (Boolean.FALSE.equals(b)) {
            log.warn("处方迁移任务正在执行中，请勿重复执行");
            return;
        }
        // 执行迁移id分发
        ThreadPoolManager.execute(() -> executeMigrationDistribution(reqDto));
    }

    // 开始执行迁移
    private void executeMigrationDistribution(MigrationPrescriptionReqDto reqDto) {

        Long minId = reqDto.getMinId();
        Long maxId = reqDto.getMaxId();
        String organSign = reqDto.getOrganSign();
        Integer pageSize = getMigrationPageSize();
        Long delayTime = getMigrationDelayTime();
        String rangeId = minId + "-" + maxId;

        try {
            // 根据 minId 按照 getMigrationPageSize() 步长递增累加
            Long currentMinId = minId;
            while (currentMinId < maxId) {
                // 检查迁移开关 - 在每次循环中判断
                if (!getMigrationSwitch()) {
                    log.warn("处方迁移开关已关闭，停止迁移任务");
                    break;
                }
                Long currentMaxId = Math.min(currentMinId + pageSize, maxId);
                // 创建分页消息
                MigrationPrescriptionEventDto eventDto = new MigrationPrescriptionEventDto()
                    .setMinId(currentMinId)
                    .setMaxId(currentMaxId)
                    .setRangeId(rangeId)
                    .setOrganSign(organSign);

                // 发送MQ消息
                migrationPrescription2EsMQProducer.sendMessage(
                    MigrationPrescriptionEvent.builder().msg(eventDto).build()
                );

                log.info("发送处方迁移MQ消息，机构：{}，ID范围：{}-{}", organSign, currentMinId, currentMaxId);

                // 延迟处理，避免对系统造成过大压力
                if (delayTime > 0) {
                    try {
                        Thread.sleep(delayTime);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("处方迁移延迟等待被中断", e);
                        break;
                    }
                }
                currentMinId = currentMaxId;
            }
            log.info("处方迁移任务分发完成，机构：{}，总范围：{}-{}", organSign, minId, maxId);

        } catch (Exception e) {
            log.error("处方迁移任务执行失败，参数：{}", JSONUtil.toJsonStr(reqDto), e);
            // 记录失败信息到数据库
            failRecordService.createMqFailRecord(organSign, minId, maxId, e.getMessage());
        } finally {
            stringRedisTemplate.delete(MIGRATION_PRESCRIPTION_FLAG_KEY);
        }

    }


    @Override
    public void migrationPrescription2Es(MigrationPrescriptionEventDto msg) {
        // 检查迁移开关
        if (!getMigrationSwitch()) {
            log.warn("处方迁移开关已关闭，跳过ES迁移任务");
            return;
        }
        Long minId = msg.getMinId();
        Long maxId = msg.getMaxId();
        String organSign = msg.getOrganSign();

        log.info("处方迁移到ES，机构：{}，ID范围：{}-{}", organSign, minId, maxId);

        // Redis键定义 累加迁移数量
        String migrationCountKey = MIGRATION_PRESCRIPTION_ES_PROGRESS_KEY + msg.getRangeId() + ":count";
        stringRedisTemplate.opsForValue().increment(migrationCountKey, maxId - minId);
        stringRedisTemplate.expire(migrationCountKey, 30, TimeUnit.DAYS);

        try {
            // 执行老系统的查询
            List<PrescriptionMigrationInfoDto> migrationInfoDtos = prescriptionMigrationApi
                .queryPrescriptionMigrationInfo(MigrationConvert.INSTANCE.convertDto(msg));

            if (CollUtil.isEmpty(migrationInfoDtos)) {
                log.info("处方迁移到ES未查询到处方数据，机构：{}，ID范围：{}-{}", organSign, minId, maxId);
                return;
            }

            log.info("处方迁移到ES进行中，机构：{}，ID范围：{}-{},数量:{}", organSign, minId, maxId, migrationInfoDtos.size());

            // 批量写入ES，支持更新已存在文档
            int successCount = batchSaveToEs(migrationInfoDtos);

            log.info("处方迁移到ES完成，机构：{}，ID范围：{}-{}，成功数量：{}", organSign, minId, maxId, successCount);

            failRecordService.createEsSuccessRecord(organSign, minId, maxId, successCount);

        } catch (Exception e) {
            log.error("处方迁移到ES，失败，机构：{}，ID范围：{}-{}", organSign, minId, maxId, e);
            // 记录失败信息到数据库
            failRecordService.createEsFailRecord(organSign, minId, maxId, e.getMessage());
        }
    }

    /**
     * 根据创建时间获取ES索引名称 格式：inquiry_history_prescription_yyyy
     */
    private String getIndexNameByCreateTime(java.util.Date createTime) {
        if (createTime == null) {
            // 如果创建时间为空，使用当前年份
            return "inquiry_history_prescription_" + LocalDateTime.now().getYear();
        }

        // 提取年份
        LocalDateTime dateTime = createTime.toInstant()
            .atZone(java.time.ZoneId.systemDefault())
            .toLocalDateTime();

        return "inquiry_history_prescription_" + dateTime.getYear();
    }

    /**
     * 根据机构号获取路由键 提取organSign中的数字部分作为路由键
     */
    private String getRoutingByOrganSign(String organSign) {
        if (StrUtil.isBlank(organSign)) {
            return "0";
        }

        // 提取数字部分
        String numbers = organSign.replaceAll("[^0-9]", "");
        if (StrUtil.isBlank(numbers)) {
            return "0";
        }

        // 取最后几位数字作为路由键，避免路由键过长
        if (numbers.length() > 3) {
            numbers = numbers.substring(numbers.length() - 3);
        }

        return numbers;
    }

    /**
     * 批量保存数据到ES，支持更新已存在文档
     */
    private int batchSaveToEs(List<PrescriptionMigrationInfoDto> migrationInfoDtos) {
        int successCount = 0;

        // 按索引分组进行批量写入
        Map<String, List<PrescriptionMigrationInfoDto>> indexGroupMap = migrationInfoDtos.stream()
            .collect(Collectors.groupingBy(info -> getIndexNameByCreateTime(info.getCreateTime())));

        // 获取路由键
        String routing = getRoutingByOrganSign(organSign);

        for (Map.Entry<String, List<PrescriptionMigrationInfoDto>> entry : indexGroupMap.entrySet()) {
            String indexName = entry.getKey();
            List<PrescriptionMigrationInfoDto> indexData = entry.getValue();

            try {
                // 确保索引存在
                IndexCoordinates indexCoordinates = IndexCoordinates.of(indexName);
                IndexOperations indexOps = elasticsearchOperations.indexOps(indexCoordinates);
                if (!indexOps.exists()) {
                    indexOps.create();
                    log.info("处方迁移到ES 创建ES索引：{}", indexName);
                }

                // 批量构建文档 - 使用save操作支持更新
                List<IndexQuery> indexQueries = new ArrayList<>();
                for (PrescriptionMigrationInfoDto prescriptionInfo : indexData) {
                    IndexQuery indexQuery = new IndexQueryBuilder()
                        .withId(String.valueOf(prescriptionInfo.getId()))
                        .withObject(prescriptionInfo)
                        .withRouting(routing)
                        .build();
                    indexQueries.add(indexQuery);
                }

                // 批量写入ES（如果文档存在则更新）
                List<IndexedObjectInformation> results = elasticsearchOperations.bulkIndex(indexQueries, indexCoordinates);
                successCount += results.size();

                log.info("处方迁移到ES 批量保存ES成功，索引：{}，数量：{}，路由：{}", indexName, results.size(), routing);

            } catch (Exception e) {
                log.error("处方迁移到ES 批量保存ES失败，索引：{}，数量：{}，", indexName, indexData.size(), e);
            }
        }

        return successCount;
    }
}
