<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-inquiry-common</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <groupId>com.xyy.saas</groupId>
  <artifactId>saas-inquiry-common-server</artifactId>
  <version>${revision}</version>
  <name>${project.artifactId}</name>
  <description>SaaS 问诊系统-公共模块服务</description>

  <dependencies>
    <!-- 引入 API 模块 -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-common-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <!-- SOA 依赖 -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>biz-soa-starter</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>yudao-module-system-api</artifactId>
          <groupId>cn.iocoder.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>


    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-excel</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>yudao-module-system-api</artifactId>
          <groupId>cn.iocoder.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <!-- 测试依赖 -->
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-product-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-pojo</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-user-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-hospital-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-pharmacist-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-drugstore-api</artifactId>
      <version>${revision}</version>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

</project>