# -- 建表语句统一说明
#     1. 统一使用自增id作为表主键 评估数据量级较大时候使用bigint类型,否则使用int类型
#     2. 所有新建表必须包含基础五个字段 creator create_time updater update_time deleted
#     3. int,bigint 类型无需显示指定长度
#     4. decimal(10, 4) 作为数据字段类型,统一保留4位小数
#     5. tinyint 当字段类型仅为是否时长度指定
#       (1) 使用 b'0'java对应的Boolean类型;  其他均指定长度为
(2) java对应的Integer类型
#     6. varchar 指定字段长度时,统一取2的指数,eg: 16 32 64 128 256
#     7. datetime 作为时间字段统一类型


#门店套餐额度表
DROP TABLE IF EXISTS saas_tenant_package_cost;
create table saas_tenant_package_cost
(
  id                bigint auto_increment comment '额度id' primary key,
  tenant_id         bigint      default 0                 not null comment '门店编号',
  biz_type          tinyint(2)  default 0                 not null comment '业务类型 0-问诊,1-智慧脸...',
  tenant_package_id bigint      default 0                 not null comment '当前在用的套餐订单表id',
  hospital_prefs    varchar(1024) null comment '问诊医院prefs',
  inquiry_way_type  tinyint(2)  default 0                 not null comment '问诊形式 1图文 2视频',
  cost              bigint      default 0                 not null comment '问诊额度 -1不限',
  surplus_cost      bigint      default 0                 not null comment '问诊剩余额度 -1不限',
  start_time        datetime    default CURRENT_TIMESTAMP not null comment '套餐开始时间',
  end_time          datetime    default CURRENT_TIMESTAMP not null comment '套餐结束时间',
  status            tinyint(2)  default 0                 not null comment '开通套餐状态, 0正常, 1暂停, 2退款, 3作废',
  version           bigint      default 0                 not null comment '版本号',

  creator           varchar(64) default ''                not null comment '创建者',
  create_time       datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater           varchar(64) default '' null comment '更新者',
  update_time       datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted           bit         default b'0'              not null comment '是否删除'
) comment '门店问诊套餐额度表';
create index idx_tenant_biz_type_package_relation_id on saas_tenant_package_cost (tenant_id, biz_type, tenant_package_id) comment '门店套餐额度索引';


#门店套餐额度变更日志表
DROP TABLE IF EXISTS saas_tenant_package_cost_log;
create table saas_tenant_package_cost_log
(
  id          bigint auto_increment comment '额度记录表id' primary key,
  tenant_id   bigint      default 0                 not null comment '门店编号',
  biz_type    tinyint(2)  default 0                 not null comment '业务类型 0-问诊,1-智慧脸...',
  cost_id     bigint      default 0                 not null comment '门店问诊套餐额度表id',
  biz_id      varchar(64) default ''                not null comment '业务id(问诊pref)',
  way_type    tinyint(2)  default 0                 not null comment '类型 (1图文 2视频)',
  change_cost bigint      default 0                 not null comment '变更额度 (扣减会存负数)',
  record_type tinyint(2)  default 0                 not null comment '记录类型 0初始创建 1问诊 2退款 3作废',

  creator     varchar(64) default ''                not null comment '创建者',
  create_time datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater     varchar(64) default '' null comment '更新者',
  update_time datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted     bit         default b'0'              not null comment '是否删除'
) comment '门店问诊套餐操作记录表';
create index idx_tenant_biz_type_cost_id on saas_tenant_package_cost_log (tenant_id, biz_type, cost_id) comment '门店套餐额度记录索引';
create index idx_biz_id_record_type on saas_tenant_package_cost_log (biz_id, record_type) comment '业务id类型记录索引';

#医生信息表
#医生信息表
DROP TABLE IF EXISTS saas_inquiry_doctor;
create table saas_inquiry_doctor
(
  id                           bigint auto_increment comment '主键' primary key,
  pref                         varchar(64)                            not null comment '医生编码',
  name                         varchar(32)                            not null comment '医生名称',
  sex                          tinyint(2)                             not null comment '性别 1男 2女',
  id_card                      varchar(32)                            not null comment '身份证号码',
  mobile                       varchar(32) null comment '手机号',
  user_id                      bigint                                 not null comment '用户ID',
  audit_status                 tinyint(2)   default 0                 not null comment '审核状态 0、待审核  1、审核通过  2、审核驳回',
  auto_grab_status             tinyint(2)   default 0                 not null comment '自动抢单状态： 0、关闭 1、自动抢单',
  online_status                tinyint(2)   default 0                 not null comment '出诊状态：0闭诊 1出诊',

  cooperation                  tinyint(2)   default 0                 not null comment '合作状态：0未合作 1 合作中 2禁用合作 3过期',
  start_inquiry_time           datetime     default CURRENT_TIMESTAMP not null comment '开始接诊时间',
  end_inquiry_time             datetime     default CURRENT_TIMESTAMP not null comment '结束停诊时间',
  env_tag                      varchar(16)  default 'prod'            not null comment '环境标志：prod-真实数据；test-测试数据；show-线上演示数据',
  photo                        varchar(512) default ''                not null comment '证件照地址',
  biography                    varchar(512) default ''                not null comment '个人简介',
  professional_dec             varchar(256) default ''                not null comment '擅长专业,eg:擅长神经内科诊疗',
  job_type                     tinyint(2)   default 0                 not null comment '医生类型： 1全职医生 2兼职医生',
  prescription_password_status bit          default b'0'              not null comment '是否开启密码,0:不开启,1:开启',
  prescription_password        varchar(64)  default ''                not null comment '开方密码',

  disable                      bit          default b'0'              not null comment '是否禁用',
  creator                      varchar(64)  default ''                not null comment '创建者',
  create_time                  datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater                      varchar(64)  default '' null comment '更新者',
  update_time                  datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted                      bit          default b'0'              not null comment '是否删除'

) comment '医生信息表';
CREATE INDEX idx_user_id ON saas_inquiry_doctor (user_id) comment '医生user_id索引';
CREATE INDEX idx_pref ON saas_inquiry_doctor (pref) comment '医生pref索引';


#医生执业信息表
DROP TABLE IF EXISTS saas_inquiry_doctor_practice;
CREATE TABLE saas_inquiry_doctor_practice
(
  id                       bigint AUTO_INCREMENT COMMENT '主键' PRIMARY KEY,
  doctor_id                bigint                                 NOT NULL COMMENT '医生id',
  first_practice_name      VARCHAR(128) DEFAULT ''                NOT NULL COMMENT '第一执业机构名称',
  first_practice_level     TINYINT(2)   DEFAULT 0                 NOT NULL COMMENT '第一执业机构等级，例如：0=三甲, 1=三乙 ...',
  first_practice_dept_name VARCHAR(64)  DEFAULT ''                NOT NULL COMMENT '第一执业机构科室名称',
  title_code               TINYINT(2)                             NOT NULL COMMENT '专业职称代码，例如：2',
  title_no                 VARCHAR(128) DEFAULT ''                NOT NULL COMMENT '专业职称证书编号',
  title_time               DATETIME COMMENT '专业职称证书取得时间，例如：2016-03-20',

  start_practice_time      DATETIME COMMENT '开始执业时间，例如：2021-03-20',
  end_practice_date        DATETIME COMMENT '执业结束时间，例如：2029-03-20',
  professional_no          VARCHAR(128) DEFAULT ''                NOT NULL COMMENT '执业证书号',
  professional_time        DATETIME COMMENT '执业证书取得时间，例如：2016-03-20',
  qualification_no         VARCHAR(128) COMMENT '资格证书号',
  qualification_time       DATETIME COMMENT '资格证书取得时间，例如：2016-03-20',
  doctor_medicare_no       VARCHAR(128) DEFAULT ''                NOT NULL COMMENT '医生医保编码',

  canal                    TINYINT(2)   DEFAULT 0                 NOT NULL COMMENT '渠道',
  inviter_name             VARCHAR(64)  DEFAULT ''                NOT NULL COMMENT '邀请人姓名',
  inviter_no               VARCHAR(32)  DEFAULT ''                NOT NULL COMMENT '邀请人手机号/工号',

  creator                  varchar(64)  default ''                not null comment '创建者',
  create_time              datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater                  varchar(64)  default '' null comment '更新者',
  update_time              datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted                  bit          default b'0'              not null comment '是否删除'
) COMMENT ='医生执业信息表';
CREATE INDEX idx_doctor_id ON saas_inquiry_doctor_practice (doctor_id) comment '医生id索引';


DROP TABLE IF EXISTS saas_inquiry_doctor_status;
#医生出诊状态关系表
create table saas_inquiry_doctor_status
(
  id               bigint auto_increment comment '主键' primary key,
  doctor_pref      varchar(64)                           not null comment '医生编码',
  inquiry_way_type tinyint(2)  default 1                 not null comment '审方类型 1:图文 2:视频 3:电话',
  inquiry_biz_type tinyint(2)  default 1                 not null comment '问诊业务类型 1:药店问诊 2:远程审方',
  inquiry_type     tinyint(2)  default 0                 not null comment '开方类型：0手动开方 1自动开方',
  status           tinyint(2)  default 0                 not null comment '出诊状态：0闭诊 1出诊',

  creator          varchar(64) default ''                not null comment '创建者',
  create_time      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater          varchar(64) default '' null comment '更新者',
  update_time      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted          bit         default b'0'              not null comment '是否删除'

) comment '医生出诊状态关系表';
CREATE INDEX idx_doctor_id_inquiry_biz_type_inquiry_way_type
  ON saas_inquiry_doctor_status (doctor_pref, inquiry_biz_type, inquiry_way_type) comment '查询医生出诊类型索引';
CREATE INDEX idx_doctor_status
  ON saas_inquiry_doctor_status (doctor_pref, status) comment '医生出诊状态索引';


#AI医生录屏记录表
DROP TABLE IF EXISTS saas_inquiry_doctor_video;
CREATE TABLE saas_inquiry_doctor_video
(
  id          bigint auto_increment comment '主键' primary key,
  pref        varchar(64)  NOT NULL DEFAULT '' COMMENT '录屏编号',
  doctor_pref varchar(64) COMMENT '医生pref',
  video_url   varchar(512) NOT NULL DEFAULT '' COMMENT '医生端问诊视频url地址',
  md5         varchar(255)          DEFAULT '' COMMENT '视频md5',
  status      tinyint(2)   NOT NULL DEFAULT 0 COMMENT '启用：0是，1否 ',

  creator     varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人',
  create_time datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater     varchar(64)  NOT NULL DEFAULT '' COMMENT '更新者',
  update_time datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted     bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除'
) COMMENT ='AI医生录屏记录表';
create index idx_pref on saas_inquiry_doctor_video (pref) comment '录屏编号索引';
create index idx_doctor_pref on saas_inquiry_doctor_video (doctor_pref) comment '医生编号索引';

#SAAS问诊备案信息表
DROP TABLE IF EXISTS saas_inquiry_filing;
create table saas_inquiry_filing
(
  id                bigint auto_increment comment '主键' primary key,
  doctor_id         bigint                                 NOT NULL COMMENT '医生id',
  #                 类型 nation_code		              tinyint(2)     default 1              not null comment '民族编码 1、汉族 ....',
  address           varchar(256) default ''                not null comment '通信地址',
  formal_level      tinyint(2) comment '学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...',
  org_province_code varchar(16)  default ''                not null comment '机构所在省份编码，eg: 420000',
  record_status     tinyint(2)   default 0                 not null comment '备案状态 1、待审核  2、审核中  3、审核通过  4、审核驳回',

  creator           varchar(64)  default ''                not null comment '创建者',
  create_time       datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater           varchar(64)  default '' null comment '更新者',
  update_time       datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted           bit          default b'0'              not null comment '是否删除'
) comment '医生备案信息表';
CREATE INDEX idx_doctor_id ON saas_inquiry_filing (doctor_id) comment '医生guid索引';

#医生收款信息表
DROP TABLE IF EXISTS saas_doctor_billing;
create table saas_doctor_billing
(
  id              bigint auto_increment comment '主键' primary key,
  doctor_id       bigint                                 NOT NULL COMMENT '医生id',
  payee_name      varchar(32)  default ''                not null comment '收款人姓名',
  payee_id_card   varchar(32)  default ''                not null comment '收款人身份证号码',
  payee_tel_phone varchar(32)  default ''                not null comment '收款人手机号',
  payee_bank_no   varchar(64)  default ''                not null comment '银行卡号',
  payee_bank_name varchar(128) default ''                not null comment '开户行',

  creator         varchar(64)  default ''                not null comment '创建者',
  create_time     datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater         varchar(64)  default '' null comment '更新者',
  update_time     datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted         bit          default b'0'              not null comment '是否删除'
) comment '医生收款信息表';
CREATE INDEX idx_doctor_id ON saas_doctor_billing (doctor_id) comment '医生guid索引';


#医生工作履历记录表
DROP TABLE IF EXISTS saas_doctor_work_record;
create table saas_doctor_work_record
(
  id             bigint auto_increment comment '主键' primary key,
  doctor_id      bigint                                 NOT NULL COMMENT '医生id',
  work_unit_name varchar(128) default ''                not null comment '工作单位名称',
  job_position   varchar(32)  default ''                not null comment '职位',
  prover         varchar(32)  default ''                not null comment '证明人',
  start_date     datetime comment '开始时间,eg:2021-03-20',
  end_date       datetime comment '结束时间,eg:2029-03-20',

  creator        varchar(64)  default ''                not null comment '创建者',
  create_time    datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater        varchar(64)  default '' null comment '更新者',
  update_time    datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted        bit          default b'0'              not null comment '是否删除'
) comment '医生工作履历记录表';
CREATE INDEX idx_doctor_id ON saas_doctor_work_record (doctor_id) comment '医生guid索引';

#医生审核记录表
DROP TABLE IF EXISTS saas_doctor_audited_record;
create table saas_doctor_audited_record
(
  id           bigint auto_increment comment '主键' primary key,
  doctor_id    bigint                                 NOT NULL COMMENT '医生id',
  auditor_name varchar(32)  default ''                not null comment '审核人姓名',
  auditor_id   bigint                                 not null comment '审核人工号',
  audit_result tinyint(2)                             not null comment '审核结果  1、审核通过  2、审核驳回',
  diff_reason  varchar(256) default ''                not null comment '驳回原因',
  audit_time   datetime     default CURRENT_TIMESTAMP not null comment '审核时间',

  creator      varchar(64)  default ''                not null comment '创建者',
  create_time  datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater      varchar(64)  default '' null comment '更新者',
  update_time  datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted      bit          default b'0'              not null comment '是否删除'
) comment '医生审核记录表';
CREATE INDEX idx_doctor_id_audit_time ON saas_doctor_audited_record (doctor_id, audit_time) comment '医生guid,审核时间索引';


#医院科室医生权限关系表
DROP TABLE IF EXISTS saas_inquiry_hospital_dept_doctor_relation;
create table saas_inquiry_hospital_dept_doctor_relation
(
  id                        bigint auto_increment comment '主键' primary key,
  hospital_pref             varchar(64)                            not null comment '医院编码',
  hospital_name             varchar(64)                            not null comment '医院名称',
  dept_pref                 varchar(64)                            not null comment '科室编码',
  dept_name                 varchar(64)                            not null comment '科室名称',
  hospital_dept_relation_id bigint                                 NOT NULL COMMENT '医院科室关系id',
  doctor_pref               varchar(64)                            not null comment '医生编码',
  doctor_name               varchar(32)                            not null comment '医生姓名',
  doctor_hospital_pref      varchar(255) default ''                not null comment '医生所在医院的编码',
  disabled                  tinyint(2)   default 0                 not null comment '是否禁用 0 否   1 是',
  inquiry_type              tinyint(2)   default 0                 not null comment '开方类型：0手动开方 1自动开方',
  inquiry_way_type          tinyint(2)   default 0                 not null comment '开方方式：1图文 2 视频  3电话',
  auto_inquiry_time         varchar(255) default ''                not null comment '自动接诊时段, 隔开  eg:08:30:00-12:00:00,13:00:00-18:00:00',
  creator                   varchar(64)  default ''                not null comment '创建者',
  create_time               datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater                   varchar(64)  default '' null comment '更新者',
  update_time               datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted                   bit          default b'0'              not null comment '是否删除'
) comment '医院医生关系表';
CREATE INDEX idx_hospital_pref ON saas_inquiry_hospital_dept_doctor_relation (hospital_pref) comment '医院pref索引';
CREATE INDEX idx_dept_pref ON saas_inquiry_hospital_dept_doctor_relation (dept_pref) comment '科室pref索引';
CREATE INDEX idx_doctor_pref ON saas_inquiry_hospital_dept_doctor_relation (doctor_pref) comment '医生pref索引';


#医院信息表
DROP TABLE IF EXISTS saas_inquiry_hospital;
create table saas_inquiry_hospital
(
  id           bigint auto_increment comment '主键' primary key,
  pref         varchar(64)                           not null comment '医院编码',
  name         varchar(256)                          not null comment '医院名称',
  level        tinyint(2)  default 0                 not null comment '医院等级',
  address      varchar(512) null comment '医院地址',
  phone        varchar(32) null comment '联系电话',
  email        varchar(64) null comment '电子邮件',
  website      varchar(256) null comment '官方网站',
  has_medicare tinyint(1)  default 0                 not null comment '是否有医保资质',
  disable      tinyint(1)  default 0                 not null comment '是否禁用',

  creator      varchar(64) default ''                not null comment '创建者',
  create_time  datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater      varchar(64) default '' null comment '更新者',
  update_time  datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted      bit         default b'0'              not null comment '是否删除'
) comment '医院信息表';


#科室字典表
DROP TABLE IF EXISTS saas_inquiry_hospital_department;
CREATE TABLE saas_inquiry_hospital_department
(
  id             bigint auto_increment comment '主键' primary key,
  pref           varchar(64)                           not null comment '科室编码',
  dept_name      varchar(64) default ''                not null comment '科室名称,eg:内科',
  dept_parent_id bigint      default 0                 not null comment '父级科室id',
  dept_order     int         default 0                 not null comment '科室序号',
  status         tinyint(2)  default 0                 not null comment '当前科室状态 0 启用 1 禁用',

  creator        varchar(64) default ''                not null comment '创建者',
  create_time    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater        varchar(64) default '' null comment '更新者',
  update_time    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted        bit         default b'0'              not null comment '是否删除'
) COMMENT = '科室字典表';
CREATE INDEX idx_parent_id ON saas_inquiry_hospital_department (dept_parent_id) comment '科室字典表父级科室id索引';

#医院科室信息表
DROP TABLE IF EXISTS saas_inquiry_hospital_department_relation;
create table saas_inquiry_hospital_department_relation
(
  id             bigint auto_increment comment '主键' primary key,
  hospital_id    bigint                                not null comment '医院id',
  hospital_pref  varchar(64) default ''                not null comment '医院编码',
  hospital_Name  varchar(64) default ''                not null comment '医院名称',
  dept_id        bigint                                not null comment '医院科室id',
  dept_pref      varchar(64) default ''                not null comment '科室编码,eg:101',
  dept_name      varchar(64) default ''                not null comment '科室名称,eg:内科',
  dept_parent_id bigint      default 0                 not null comment '父级科室id',
  disabled       tinyint(2)  default 0                 not null comment '是否禁用 0 否   1 是',

  creator        varchar(64) default ''                not null comment '创建者',
  create_time    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater        varchar(64) default '' null comment '更新者',
  update_time    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted        bit         default b'0'              not null comment '是否删除'
) comment '医院科室信息表';
CREATE INDEX idx_hospital_department
  ON saas_inquiry_hospital_department_relation (hospital_id, dept_parent_id) comment '医院科室层级索引';


#药师信息表
DROP TABLE IF EXISTS saas_inquiry_pharmacist;
create table saas_inquiry_pharmacist
(
  id              bigint auto_increment comment '主键' primary key,
  pref            varchar(64)                            not null comment '药师编码',
  user_id         bigint                                 not null comment '用户ID',
  name            varchar(32)                            not null comment '姓名',
  sex             tinyint(2)                             not null comment '性别 1男 2女',
  id_card         varchar(32)                            not null comment '身份证号码',
  mobile          varchar(32) null comment '联系电话',
  audit_status    tinyint(2)   default 0                 not null comment '审核状态 0、待审核  1、审核通过  2、审核驳回',
  online_status   tinyint(2)   default 0                 not null comment '在线状态：0闭诊 1出诊',
  cooperation     tinyint(2)   default 0                 not null comment '合作状态：0未合作 1 合作中 2禁用合作 3过期',
  photo           varchar(512) default ''                not null comment '证件照地址',
  biography       varchar(512) default ''                not null comment '个人简介',
  qualification   tinyint(2)   default 0                 not null comment '药师执业资格,中药或西药',
  pharmacist_type tinyint(2)   default 0                 not null comment '药师类型 平台药师 / 门店药师 / 医院药师',
  school          varchar(64)  default ''                not null comment '毕业学校',
  nation_code     tinyint(2)   default 1                 not null comment '民族编码 1、汉族 ....',
  formal_level    tinyint(2) comment '学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...',
  address         varchar(256) default ''                not null comment '通信地址',
  finger_print    text null comment '药师指纹',
  drawn_sign      tinyint(2)   default 0                 not null comment '是否手绘签名(不走认证自己绘制)：0否 1是',
  remark          varchar(256) default '' null comment '备注',
  env_tag         varchar(16)  default 'prod'            not null comment '环境标志：prod-真实数据；test-测试数据；show-线上演示数据',

  disable         bit          default b'0'              not null comment '是否禁用',
  creator         varchar(64)  default ''                not null comment '创建者',
  create_time     datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater         varchar(64)  default '' null comment '更新者',
  update_time     datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted         bit          default b'0'              not null comment '是否删除'
) comment '药师信息表';
CREATE INDEX idx_user_id ON saas_inquiry_pharmacist (user_id) comment '药师user_id索引';


#问诊职业
(医生药师)证件信息表
DROP TABLE IF EXISTS saas_inquiry_profession_identification;
create table saas_inquiry_profession_identification
(
  id                  bigint auto_increment comment '主键' primary key,
  person_id           bigint                                  not null comment '医生|药师id',
  doctor_type         tinyint(2)    default 1                 not null comment '类型,1医生,2药师',
  certificate_type    tinyint(2)    default 1                 not null comment '证件类型 1、头像 2、查证结果 3、职称证明 4、执业证 5、资格证',
  certificate_name    varchar(256)  default ''                not null comment '证件名称',
  certificate_no      varchar(256)  default ''                not null comment '证件号',
  certificate_img_url varchar(1024) default ''                not null comment '证件地址',
  register_time       datetime null comment '注册发证日期',
  valid_time          datetime null comment '有效期',

  creator             varchar(64)   default ''                not null comment '创建者',
  create_time         datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
  updater             varchar(64)   default '' null comment '更新者',
  update_time         datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted             bit           default b'0'              not null comment '是否删除'
) comment '问诊职业(医生药师)证件信息表';
CREATE INDEX idx_person_id ON saas_inquiry_profession_identification (person_id) comment 'person_id索引';


#处方笺模板表
DROP TABLE IF EXISTS saas_inquiry_prescription_template;
create table saas_inquiry_prescription_template
(
  id              bigint auto_increment comment '主键' primary key,
  hospital_pref   varchar(64)   default ''                not null comment '医院编码',
  name            varchar(256)  default ''                not null comment '处方笺模板名称',
  type            tinyint(2)    default 0                 not null comment '处方笺模板类型',
  `desc`          varchar(1024) default ''                not null comment '处方笺模板描述',
  `disable`       bit           default b'0'              not null comment '是否禁用',
  url0            varchar(1024)                           not null comment '文件 URL（底版）',
  url             varchar(1024)                           not null comment '文件 URL',
  template_fields text null comment '模板字段',

  creator         varchar(64)   default ''                not null comment '创建者',
  create_time     datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
  updater         varchar(64)   default '' null comment '更新者',
  update_time     datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted         bit           default b'0'              not null comment '是否删除'
) comment '处方笺模板表';
CREATE INDEX idx_hospital_pref ON saas_inquiry_prescription_template (hospital_pref) comment 'hospital_pref索引';

#
处方模版去掉所属医院
alter table saas_inquiry_prescription_template
drop
column hospital_pref,
drop index idx_hospital_pref;


#问诊记录
DROP TABLE IF EXISTS saas_inquiry_record;
#问诊记录表
CREATE TABLE saas_inquiry_record
(
  id                  bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  pref                varchar(64)  NOT NULL DEFAULT '' COMMENT '问诊编号',
  patient_pref        varchar(64)  NOT NULL DEFAULT '' COMMENT '患者编号',
  patient_name        varchar(32)  NOT NULL DEFAULT '' COMMENT '患者姓名',
  patient_age         varchar(4)   NOT NULL DEFAULT '' COMMENT '患者年龄',
  patient_sex         tinyint(1)   NOT NULL DEFAULT '0' COMMENT '患者性别 1、男   2、女',
  patient_mobile      varchar(20)  NOT NULL DEFAULT '' COMMENT '患者手机号',
  hospital_pref       varchar(64)  NOT NULL DEFAULT '' COMMENT '互联网医院编号',
  hospital_name       varchar(64)  NOT NULL DEFAULT '' COMMENT '互联网医院名称',
  dept_pref           varchar(64)  NOT NULL DEFAULT '' COMMENT '科室编码',
  dept_name           varchar(64)  NOT NULL DEFAULT '' COMMENT '科室名称',
  pre_temp_id         bigint(20)   NOT NULL DEFAULT 0 COMMENT '处方笺模版编号',
  doctor_pref         varchar(64)  NOT NULL DEFAULT '' COMMENT '医生编号',
  doctor_name         varchar(32)  NOT NULL DEFAULT '' COMMENT '医生姓名',
  inquiry_status      tinyint(2)   NOT NULL DEFAULT '0' COMMENT '接诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方',
  cancel_reason       varchar(255) NOT NULL DEFAULT '' COMMENT '取消开方原因',
  inquiry_way_type    tinyint(2)   NOT NULL DEFAULT '1' COMMENT '问诊方式  1、图文问诊  2、视频问诊  3、电话问诊',
  inquiry_biz_type    tinyint(2)   NOT NULL DEFAULT '1' COMMENT '问诊业务类型 1、药店问诊  2、远程审方',
  client_channel_type tinyint(2)   NOT NULL DEFAULT '0' COMMENT '客户端渠类型 0、app  1、小程序  2、PC ',
  client_os_type      varchar(255) NOT NULL DEFAULT '' COMMENT '客户端系统类型 前端传入',
  biz_channel_type    int(11)   NOT NULL DEFAULT '0' COMMENT '问诊渠道 0、荷叶 1、智慧脸  2、海典ERP',
  medicine_type       tinyint(2)   NOT NULL DEFAULT '0' COMMENT '用药类型：0西药  、1中药',
  auto_inquiry        tinyint(2)   NOT NULL DEFAULT '0' COMMENT '是否自动开方：0 否  、 1是',
  auto_grab_status    tinyint(2)   NOT NULL DEFAULT '0' COMMENT '自动抢派单状态：0 常规派单  1、自动抢单',
  unable_auto_reason  tinyint(2)            DEFAULT NULL COMMENT '不走自动开方的原因  1、预购药品无用法用量 2、无自动开方医生  3、门店未开通自动开方',
  im_platform         tinyint(2)   NOT NULL DEFAULT '0' COMMENT 'IM平台类型  0、腾讯IM',
  order_no            varchar(64)  NOT NULL DEFAULT '' COMMENT '订单编号',
  tenant_id           bigint(20)   NOT NULL DEFAULT '0' COMMENT '租户ID',
  tenant_name         varchar(64)  NOT NULL DEFAULT '' COMMENT '租户名称',
  start_time          datetime              DEFAULT NULL COMMENT '医生接诊时间',
  end_time            datetime              DEFAULT NULL COMMENT '问诊结束时间',
  doctor_video_pref   varchar(64)  NOT NULL DEFAULT '' COMMENT '医生录屏视频编码',
  stream_status       tinyint(2)   NOT NULL DEFAULT 0 COMMENT '视频流状态 0 未视频 1 已推流 2 已开启转推 3 转推结束',
  mp4_url             varchar(255) NOT NULL DEFAULT '' COMMENT '视频地址',
  im_pdf              varchar(255) NOT NULL DEFAULT '' COMMENT 'im问诊记录',
  im_history          text COMMENT 'im问诊记录',
  stream_id           varchar(128) NOT NULL DEFAULT '' COMMENT '视频混流id',
  transcoding_id      varchar(256) NOT NULL DEFAULT '' COMMENT '任务ID',
  creator             varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人，小程序问诊存扫码人的userId',
  create_time         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater             varchar(64)  NOT NULL DEFAULT '' COMMENT '更新者',
  update_time         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted             bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (id)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='问诊记录表';


#问诊记录详情表
DROP TABLE IF EXISTS saas_inquiry_record_detail;
CREATE TABLE saas_inquiry_record_detail
(
  id                        bigint auto_increment comment '主键' primary key,
  tenant_id                 bigint(20)  NOT NULL COMMENT '租户ID',
  inquiry_pref              varchar(64) NOT NULL COMMENT '问诊单pref',
  liver_kidney_value        tinyint(2)  NOT NULL DEFAULT 0 COMMENT '肝肾功能异常  0、无  1、肝功能异常  2、肾功能异常  3、肝肾功能异常',
  gestation_lactation_value tinyint(2)  NOT NULL DEFAULT 0 COMMENT '妊娠哺乳期   0、否  1、妊娠期   2、哺乳期  3、妊娠哺乳期',
  patient_pref              varchar(64) COMMENT '患者pref',
  patient_name              varchar(20) COMMENT '患者姓名',
  patient_sex               tinyint(2) COMMENT '患者性别：1 男 2 女',
  patient_age               varchar(4) COMMENT '患者年龄',
  patient_mobile            varchar(20) COMMENT '患者手机号',
  patient_id_card           varchar(32) COMMENT '患者身份证号',
  slow_disease              tinyint(2)  NOT NULL DEFAULT 0 COMMENT '慢病病情需要 0 否  1是',
  prescription_type         tinyint(2)  null comment '处方类型',
  main_suit                 varchar(512)         DEFAULT '' COMMENT '主诉',
  allergic                  varchar(512)         DEFAULT '' COMMENT '过敏史  eg：青霉素|头孢',
  diagnosis_code            varchar(512)         DEFAULT '' COMMENT '诊断编码',
  diagnosis_name            varchar(512)         DEFAULT '' COMMENT '诊断说明',
  patient_his_desc          varchar(255)         DEFAULT '' COMMENT '个人史',
  current_Illness_desc      varchar(255)         DEFAULT '' COMMENT '现病史',
  offline_prescriptions     varchar(512)         DEFAULT '' COMMENT '线下就医处方或病历图片',
  pre_drug_detail           text COMMENT '预购药明细',
  ext                       text COMMENT '问诊扩展字段',
  remarks                   varchar(100)         DEFAULT '' COMMENT '备注说明',
  creator                   varchar(64) NOT NULL DEFAULT '' COMMENT '创建人，小程序问诊存扫码人的userId',
  create_time               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater                   varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted                   bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='问诊记录详情表';


#医疗就诊登记
(挂号)表
DROP TABLE IF EXISTS saas_medical_registration;
CREATE TABLE saas_medical_registration
(
  id                 bigint auto_increment comment '主键' primary key,
  pref               varchar(64) NOT NULL DEFAULT '' COMMENT '就诊编号',
  biz_id             varchar(64) NOT NULL COMMENT '业务id',
  biz_type           tinyint(2)           default 0 not null comment '业务类型 0-问诊,1-智慧脸...',

  tenant_id          bigint(20)  NOT NULL COMMENT '门店ID',
  patient_pref       varchar(64) COMMENT '患者pref',
  patient_name       varchar(20) COMMENT '患者姓名',
  patient_mobile     varchar(20) COMMENT '患者手机号',
  patient_id_card    varchar(32) COMMENT '患者身份证号',

  biz_visit_id       varchar(64) NOT NULL DEFAULT '' COMMENT '业务就诊流水号',
  medical_visit_id   varchar(64) NOT NULL DEFAULT '' COMMENT '医保就诊id',
  medical_visit_date datetime NULL COMMENT '医保就诊登记时间',
  med_type           varchar(12)          DEFAULT '' NOT NULL COMMENT '医疗类别码',
  insured_area_no    varchar(32)          DEFAULT '' NOT NULL COMMENT '参保地编号',
  tenant_area_no     varchar(32)          DEFAULT '' NOT NULL COMMENT '就医地编号',
  psn_no             varchar(64)          DEFAULT '' NOT NULL COMMENT '参保人员编号',
  ipt_otp_no         varchar(64)          DEFAULT '' NOT NULL COMMENT '住院/门诊号',
  status             tinyint(2)           DEFAULT 0 NOT NULL COMMENT '单据状态',
  dept_pref          varchar(64)          DEFAULT NULL COMMENT '登记科室编号',
  dept_name          varchar(64)          DEFAULT NULL COMMENT '登记科室名称',
  hospital_pref      varchar(64) NOT NULL DEFAULT '' COMMENT '医院编号',
  hospital_name      varchar(64) NOT NULL DEFAULT '' COMMENT '医院名称',
  acct_used_flag     tinyint(2)  NULL COMMENT '个人账户使用标志 0|不使用,1|使用',
  book_time          datetime NULL COMMENT '预约登记时间',
  plan_time          datetime NULL COMMENT '预约执行日期',

  ext                text COMMENT '就诊挂号登记扩展字段',
  creator            varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  create_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater            varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted            bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) COMMENT ='医疗就诊登记(挂号)表';
create index idx_pref_biz_type on saas_medical_registration (pref, biz_type) comment '就诊编号+类型索引';
create index idx_tenant_id_biz_id_type on saas_medical_registration (tenant_id, biz_id, biz_type) comment '就诊业务id索引';
create index idx_biz_visit_id_type on saas_medical_registration (biz_visit_id, biz_type) comment '业务就诊流水索引';


#问诊患者病例表
DROP TABLE IF EXISTS saas_inquiry_clinical_case;
CREATE TABLE saas_inquiry_clinical_case
(
  id                        bigint auto_increment comment '主键' primary key,
  pref                      varchar(64) NOT NULL DEFAULT '' COMMENT '病例编号',
  inquiry_pref              varchar(64) NOT NULL COMMENT '问诊编号',
  tenant_id                 bigint(20)  NOT NULL COMMENT '租户ID',
  tenant_name               varchar(64) NOT NULL DEFAULT '' COMMENT '租户名称',
  hospital_pref             varchar(64) NOT NULL DEFAULT '' COMMENT '互联网医院编号',
  hospital_name             varchar(64) NOT NULL DEFAULT '' COMMENT '互联网医院名称',
  doctor_pref               varchar(64) NOT NULL DEFAULT '' COMMENT '医生编号',
  doctor_name               varchar(32) NOT NULL DEFAULT '' COMMENT '医生姓名',
  dept_pref                 varchar(64) NOT NULL DEFAULT '' COMMENT '科室编码',
  dept_name                 varchar(64) NOT NULL DEFAULT '' COMMENT '科室名称',
  patient_pref              varchar(64) COMMENT '患者pref',
  patient_name              varchar(20) COMMENT '患者姓名',
  patient_id_card           varchar(32) COMMENT '患者身份证号',
  patient_age               varchar(4)  NOT NULL DEFAULT '' COMMENT '患者年龄',
  patient_sex               tinyint(2)  NOT NULL DEFAULT '0' COMMENT '患者性别 1、男   2、女',

  main_suit                 varchar(512)         default '' null comment '主诉',
  allergic                  varchar(512)         default '' null comment '过敏史  eg：青霉素|头孢',
  patient_his_desc          varchar(255)         default '' null comment '既往史',
  current_Illness_desc      varchar(255)         default '' null comment '现病史',
  follow_up                 tinyint(2)           DEFAULT 1 NOT NULL COMMENT '复诊标识 0初次 1复诊',
  main_symptoms             varchar(1024)        DEFAULT '' COMMENT '主要症状',
  measures                  varchar(512)         DEFAULT '' COMMENT '处理措施',
  observation               tinyint(2)           DEFAULT 0 NOT NULL COMMENT '是否需要留院观察 0否 1是',
  referral                  tinyint(2)           DEFAULT 0 NOT NULL COMMENT '转诊标识 0非转诊',
  diagnosis_code            varchar(512)         DEFAULT '' COMMENT '西医诊断编码',
  diagnosis_name            varchar(512)         DEFAULT '' COMMENT '西医诊断名称',
  tcm_diagnosis_code        varchar(512)         DEFAULT '' COMMENT '中医诊断编码',
  tcm_diagnosis_name        varchar(512)         DEFAULT '' COMMENT '中医诊断名称',
  tcm_syndrome_code         varchar(512)         DEFAULT '' COMMENT '中医辨证代码',
  tcm_syndrome_name         varchar(512)         DEFAULT '' COMMENT '中医辨证名称',
  tcm_treatment_method_code varchar(512)         DEFAULT '' COMMENT '中医治法代码',
  tcm_treatment_method_name varchar(512)         DEFAULT '' COMMENT '中医治法名称',

  ext                       text COMMENT '病例扩展字段',
  creator                   varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  create_time               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater                   varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted                   bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) COMMENT ='门诊患者病例表';
create index idx_inquiry_pref on saas_inquiry_clinical_case (inquiry_pref) comment '问诊编号索引';


DROP TABLE IF EXISTS saas_inquiry_patient_info;
#患者信息表
CREATE TABLE saas_inquiry_patient_info
(
  id            bigint auto_increment comment '主键' primary key,
  tenant_id     bigint(20)  NOT NULL COMMENT '租户ID',
  pref          varchar(64) NOT NULL COMMENT '患者编码',
  name          varchar(20) NOT NULL DEFAULT '' COMMENT '患者姓名',
  sex           tinyint(2)  NOT NULL DEFAULT 1 COMMENT '患者性别：1 男 2 女',
  age           varchar(4)  NOT NULL DEFAULT 0 COMMENT '患者年龄',
  birthday      datetime COMMENT '出生日期',
  mobile        varchar(20) NOT NULL DEFAULT '' COMMENT '患者手机号',
  id_card       varchar(32) NOT NULL DEFAULT '' COMMENT '患者身份证号码',
  source        int(11)  NOT NULL DEFAULT 0 COMMENT '患者来源：0、荷叶问诊   1、智慧脸  2、海典ERP',
  third_user_id varchar(64) COMMENT '三方系统患者id',
  creator       varchar(64) NOT NULL DEFAULT '' COMMENT '创建人，小程序问诊存扫码人的userId',
  create_time   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater       varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time   datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted       bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4 COMMENT ='患者信息表';


DROP TABLE IF EXISTS saas_inquiry_prescription;
#处方记录表
CREATE TABLE saas_inquiry_prescription
(
  id                      bigint auto_increment comment '主键' primary key,
  pref                    varchar(64)   NOT NULL DEFAULT '' COMMENT '处方编号',
  inquiry_pref            varchar(64)   NOT NULL DEFAULT '' COMMENT '问诊单pref',
  hospital_pref           varchar(64)   NOT NULL DEFAULT '' COMMENT '互联网医院编码',
  pre_temp_id             bigint        NOT NULL DEFAULT 0 COMMENT '处方笺模版GUID',
  dept_pref               varchar(64)   NOT NULL DEFAULT '' COMMENT '科室编码',
  dept_name               varchar(64)   NOT NULL DEFAULT '' COMMENT '科室名称',
  disease_cases_pref      varchar(64)   NOT NULL DEFAULT '' COMMENT '病历编号',
  patient_pref            varchar(64)   NOT NULL DEFAULT '' COMMENT '患者编码',
  patient_name            varchar(20)   NOT NULL DEFAULT '' COMMENT '患者姓名',
  patient_sex             tinyint(2)    NOT NULL DEFAULT 1 COMMENT '患者性别：1 男 2 女',
  patient_age             varchar(3)    NOT NULL DEFAULT 0 COMMENT '患者年龄',
  patient_mobile          varchar(20)   NOT NULL DEFAULT '' COMMENT '患者手机号',
  doctor_pref             varchar(64)   NOT NULL DEFAULT '' COMMENT '医师编码',
  doctor_name             varchar(32)   NOT NULL DEFAULT '' COMMENT '医师姓名',
  pharmacist_pref         varchar(64)   NOT NULL DEFAULT '' COMMENT '药师编码',
  pharmacist_name         varchar(32)   NOT NULL DEFAULT '' COMMENT '药师姓名',
  tenant_id               bigint(20)    NOT NULL DEFAULT 0 COMMENT '租户id',
  tenant_name             varchar(64)   NOT NULL DEFAULT '' COMMENT '租户名称',
  auto_inquiry            tinyint(2)    NOT NULL DEFAULT 1 COMMENT '是否自动开方：0 否  、 1是',
  audit_level             tinyint(2)    NOT NULL DEFAULT 1 COMMENT '处方审核级数',
  status                  tinyint(2)    NOT NULL DEFAULT '0' COMMENT '处方状态 待开方-0  已取消-1  待审核-2  审核中-3 已审核-4 审核驳回-5',
  auditor_type            tinyint(2)             default 1 not null comment '当前审方类型 1-医生,2-药店,3-平台,4-医院',
  distribute_status       tinyint(2)             default 0 not null comment '处方分发状态 0-未分配,1-已分配',
  distribute_user_id      bigint null comment '处方分配的用户id',
  medicine_type           tinyint(2)    NOT NULL DEFAULT '0' COMMENT '用药类型：0西药，1中药',
  use_status              tinyint(2)    NOT NULL DEFAULT '0' COMMENT '使用状态：0 初始 1可用 2已用 3过期  4失效',
  invalid_reason          varchar(1024) NOT NULL DEFAULT '' COMMENT '失效原因',
  invalid_time            datetime               DEFAULT NULL COMMENT '失效时间',
  fee_type                varchar(32)   NOT NULL DEFAULT '' COMMENT '费别',
  inquiry_start_time      datetime COMMENT '问诊开始时间',
  inquiry_end_time        datetime COMMENT '问诊结束时间',
  out_prescription_time   datetime COMMENT '医师出方时间',
  audit_prescription_time datetime COMMENT '药师审核时间',
  order_no                varchar(32)   NOT NULL DEFAULT '' COMMENT '订单号',
  third_prescription_no   varchar(64)   NOT NULL DEFAULT '' COMMENT '三方系统处方编号',
  main_suit               varchar(512)  NOT NULL DEFAULT '' COMMENT '主诉',
  diagnosis_code          varchar(512)  NOT NULL DEFAULT '' COMMENT '诊断编码',
  diagnosis_name          varchar(512)  NOT NULL DEFAULT '' COMMENT '诊断说明',
  prescription_img_url    varchar(128)  NOT NULL DEFAULT '' COMMENT '处方笺图片url',
  prescription_pdf_url    varchar(128)  NOT NULL DEFAULT '' COMMENT '处方笺PDFurl',
  case_img_url            varchar(32)   NOT NULL DEFAULT '' COMMENT '病历img图片url',
  inquiry_way_type        tinyint(2)    NOT NULL DEFAULT 1  COMMENT '问诊方式  1、图文问诊  2、视频问诊  3、电话问诊',
  inquiry_biz_type        tinyint(2)    NOT NULL DEFAULT 1  COMMENT '问诊业务类型 1、药店问诊  2、远程审方',
  client_channel_type     tinyint(2)    NOT NULL DEFAULT 0 COMMENT '客户端渠类型 0、app  1、pc  2、小程序 ',
  doctor_channel_type     tinyint(2)    NOT NULL DEFAULT 0 COMMENT '医生客户端类型 0、app  1、pc',
  doctor_os_type          varchar(255)  NOT NULL DEFAULT 0 COMMENT '医生操作系统类型 前端传入',
  biz_channel_type        int(11)    NOT NULL DEFAULT '0' COMMENT '问诊渠道 0、荷叶 1、智慧脸  2、海典ERP',
  print_status            tinyint(2)    NOT NULL DEFAULT '0' COMMENT '处方打印状态（0-未打印、1-已打印、NULL -未知）',
  sign_platform           tinyint(2)    NOT NULL DEFAULT '1' COMMENT '签章平台类型 1、法大大  2、自绘',
  ext                     varchar(4096) NOT NULL DEFAULT '' COMMENT '处方拓展字段',
  creator                 varchar(64)   NOT NULL DEFAULT '' COMMENT '创建人，小程序问诊存扫码人的userId',
  create_time             datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater                 varchar(64)   NOT NULL DEFAULT '' COMMENT '更新者',
  update_time             datetime      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted                 bit(1)        NOT NULL DEFAULT b'0' COMMENT '是否删除'
) COMMENT ='处方记录表';
create index idx_third_prescription_no on saas_inquiry_prescription (third_prescription_no) comment '三方处方单号索引';
create index idx_pref on saas_inquiry_prescription (pref) comment '处方编号索引';
create index idx_inquiry_pref on saas_inquiry_prescription (inquiry_pref) comment '问诊单索引';
create index idx_out_prescription_time_tenant_id_status on saas_inquiry_prescription (tenant_id, status, out_prescription_time) comment '开方时间索引';
create index idx_out_prescription_time_doctor_status on saas_inquiry_prescription (out_prescription_time, doctor_pref, status) comment '开方时间索引';
create index idx_audit_prescription_time_pharmacist_status on saas_inquiry_prescription (audit_prescription_time, pharmacist_pref, status) comment '开方时间索引';
create index idx_audit_prescription_time_tenant_id_status on saas_inquiry_prescription (audit_prescription_time, tenant_id, status) comment '开方时间索引';

#处方记录详情表
DROP TABLE IF EXISTS saas_inquiry_prescription_detail;
CREATE TABLE saas_inquiry_prescription_detail
(
  id                      bigint auto_increment comment '主键' primary key,
  prescription_pref       varchar(64)    NOT NULL DEFAULT '' COMMENT '处方编号',
  inquiry_pref            varchar(64)    NOT NULL DEFAULT '' COMMENT '问诊单pref',
  tenant_id               bigint         NOT NULL DEFAULT 0 COMMENT '租户id',
  tenant_name             varchar(64)    NOT NULL DEFAULT '' COMMENT '租户名称',
  product_pref            varchar(64)    NOT NULL DEFAULT '' COMMENT '商品编码',
  standard_id             varchar(64)    NOT NULL DEFAULT '' COMMENT '标准库id',
  product_name            varchar(255)   NOT NULL DEFAULT '' COMMENT '商品名称',
  common_name             varchar(255)   NOT NULL DEFAULT '' COMMENT '通用名称',
  directions              varchar(255)   NOT NULL DEFAULT '' COMMENT '用药方法eg:口服',
  single_dose             varchar(255)   NOT NULL DEFAULT '' COMMENT '单次剂量 eg:1',
  single_unit             varchar(255)   NOT NULL DEFAULT '' COMMENT '单次剂量单位 eg:片',
  use_frequency           varchar(64)    NOT NULL DEFAULT '' COMMENT '使用频率 eg:一日三次',
  medicine_type           tinyint(2)     NOT NULL DEFAULT '0' COMMENT '药品类型：0西药，1中药',
  quantity                decimal(10, 3) NOT NULL DEFAULT '0.000' COMMENT '数量',
  attribute_specification varchar(255)   NOT NULL DEFAULT '' COMMENT '商品规格',
  manufacturer            varchar(255)   NOT NULL DEFAULT '' COMMENT '生产厂家',
  producing_area          varchar(255)   NOT NULL DEFAULT '' COMMENT '产地',
  approval_number         varchar(255)   NOT NULL DEFAULT '' COMMENT '批准文号',
  package_unit            varchar(255)   NOT NULL DEFAULT '' COMMENT '包装单位名称',
  product_system_type     int(11)        NOT NULL DEFAULT '0' COMMENT '商品系统类型',
  prescription_yn         tinyint(2)              DEFAULT '0' COMMENT '是否处方药:0否，1是',
  product_price           decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '商品价格',
  actual_amount           decimal(10, 3) NOT NULL DEFAULT '0.000' COMMENT '实收金额',

  creator                 varchar(64)    NOT NULL DEFAULT '' COMMENT '创建人',
  create_time             datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater                 varchar(64)    NOT NULL DEFAULT '' COMMENT '更新者',
  update_time             datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted                 bit(1)         NOT NULL DEFAULT b'0' COMMENT '是否删除'
) COMMENT ='处方记录详情表';

create index idx_prescription_pref on saas_inquiry_prescription_detail (prescription_pref) comment '处方编号索引';
create index idx_create_time_tenant_id on saas_inquiry_prescription_detail (create_time, tenant_id) comment '时间+门店索引';



DROP TABLE IF EXISTS saas_inquiry_prescription_audit;
#处方审核记录表
CREATE TABLE saas_inquiry_prescription_audit
(
  id                      bigint auto_increment comment '主键' primary key,
  tenant_id               bigint(20)                             NOT NULL COMMENT '门店ID',
  pref                    varchar(64)                            NOT NULL DEFAULT '' COMMENT '处方编号',
  auditor_type            tinyint(2)                             NOT NULL COMMENT '审核人类型 1:医生 2:平台药师 3:药店药师',
  audit_level             tinyint(2)   DEFAULT '1' COMMENT '记录审批级数',
  audit_status            tinyint(2)   DEFAULT 0                 NOT NULL COMMENT '审核状态 0:待审核 1:审核中 2:通过 3:驳回  4:超时',
  auditor_id              bigint       DEFAULT NULL COMMENT '审核人(药师)id',
  auditor_name            varchar(24)  DEFAULT NULL COMMENT '审核人(药师)名称',
  audit_approval_type     tinyint(2)   DEFAULT NULL COMMENT '审方类型 1:图文 2:视频 3:电话',
  auditor_receive_time    datetime     DEFAULT NULL COMMENT '领单时间',
  auditor_approval_time   datetime     DEFAULT NULL COMMENT '药师审批时间',
  auditor_rejected_reason varchar(255) DEFAULT NULL COMMENT '药师驳回原因',
  auditor_ca_sign         varchar(255) DEFAULT NULL COMMENT '药师签名',
  auditor_sign_img_url    varchar(255) DEFAULT NULL COMMENT '药师签名图片',
  signature_status        tinyint(2)   DEFAULT NULL COMMENT '签章状态 1:待签章 2:发起签章 3:已签章',
  auditor_signature_time  datetime     DEFAULT NULL COMMENT '审核人 (药师)发起签名时间',
  auditor_callback_time   datetime     DEFAULT NULL COMMENT '审核人 (药师)签名回调时间',
  client_channel_type     int(11)      DEFAULT NULL COMMENT '审方端客户端类型 0、app  1、小程序  2、PC ',
  client_os_type          varchar(255) DEFAULT NULL COMMENT '审核 (药师)端操作系统类型 前端传入',
  creator                 varchar(64)  default ''                not null comment '创建者',
  create_time             datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater                 varchar(64)  default '' null comment '更新者',
  update_time             datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted                 bit          default b'0'              not null comment '是否删除'
) COMMENT ='处方审核记录表';
create index idx_pref on saas_inquiry_prescription_audit (pref) comment '处方编号索引';
create index idx_tenant_id_create_time on saas_inquiry_prescription_audit (tenant_id, create_time) comment '门店创建时间索引';
create index idx_auditor_id_approval_time on saas_inquiry_prescription_audit (auditor_id, auditor_approval_time) comment '审批人审批时间索引';


#医生问诊评价表
DROP TABLE IF EXISTS saas_doctor_reviews;
create table saas_doctor_reviews
(
  id                 BIGINT AUTO_INCREMENT COMMENT '主键' primary key,
  inquiry_pref       varchar(64) unique NOT NULL DEFAULT '' COMMENT '问诊单pref',
  prescription_pref  varchar(64)        NOT NULL DEFAULT '' COMMENT '处方编号',
  doctor_pref        varchar(64)        NOT NULL DEFAULT '' COMMENT '医师编码',
  satisfaction_score DECIMAL(10, 1)     NOT NULL DEFAULT '0.0' COMMENT '满意度评分',
  satisfaction_item  varchar(64)        NOT NULL DEFAULT '' COMMENT '满意的点',
  reviews_content    varchar(64)        NOT NULL DEFAULT '' COMMENT '评论内容',

  creator            varchar(64)                 default '' not null comment '创建者',
  create_time        datetime                    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater            varchar(64)                 default '' null comment '更新者',
  update_time        datetime                    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted            bit                         default b'0' not null comment '是否删除'
) comment '医生问诊评价表';
create index idx_inquiry_pref on saas_doctor_reviews (inquiry_pref) comment '医生评价问诊单号索引';
create index idx_doctor_pref on saas_doctor_reviews (doctor_pref) comment '医生评价医生编码索引';


#问诊投诉记录表
DROP TABLE IF EXISTS saas_inquiry_complain;
create table saas_inquiry_complain
(
  id               BIGINT AUTO_INCREMENT COMMENT '主键' primary key,
  inquiry_pref     varchar(64)   NOT NULL DEFAULT '' COMMENT '问诊单pref',
  complain_user    BIGINT        NOT NULL DEFAULT 0 COMMENT '投诉人id',
  be_complain_user BIGINT        NOT NULL DEFAULT 0 COMMENT '被投诉人id',
  complain_item    varchar(64)   NOT NULL DEFAULT '' COMMENT '举报类型',
  complain_content varchar(256)  NOT NULL DEFAULT '' COMMENT '举报描述',
  complain_image   varchar(1024) NOT NULL DEFAULT '' COMMENT '证明材料图片',

  creator          varchar(64)            default '' not null comment '创建者',
  create_time      datetime               default CURRENT_TIMESTAMP not null comment '创建时间',
  updater          varchar(64)            default '' null comment '更新者',
  update_time      datetime               default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted          bit                    default b'0' not null comment '是否删除'
) comment '问诊投诉记录表';
create index idx_inquiry_pref on saas_inquiry_complain (inquiry_pref) comment '问诊举报单号索引';
create index idx_complain_user on saas_inquiry_complain (complain_user) comment '投诉者索引';
create index idx_be_complain_user on saas_inquiry_complain (be_complain_user) comment '被投诉者索引';


#医生常用语
DROP TABLE IF EXISTS saas_doctor_quick_reply_msg;
create table saas_doctor_quick_reply_msg
(
  id          bigint auto_increment comment '主键'
    primary key,
  parent_id   bigint null comment '父级id',
  doctor_id   bigint                                 not null comment '医生id',
  title       varchar(40)                            not null comment '分类标题',
  content     varchar(200) default ''                not null comment '常用语内容',
  sorted      int          default 0 null comment '排序',

  creator     varchar(64)  default ''                not null comment '创建者',
  create_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater     varchar(64)  default '' null comment '更新者',
  update_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted     bit          default b'0'              not null comment '是否删除'
) comment '医生快捷回复语表';

create index idx_doctor_id on saas_doctor_quick_reply_msg (doctor_id) comment '医生doctor_id索引';

#诊断信息表
DROP TABLE IF EXISTS saas_inquiry_diagnosis;
create table saas_inquiry_diagnosis
(
  id             bigint auto_increment comment '主键' primary key,
  diagnosis_code varchar(64)                           NOT NULL DEFAULT '' COMMENT '诊断编码',
  diagnosis_name varchar(64)                           NOT NULL DEFAULT '' COMMENT '诊断名称',
  diagnosis_type tinyint(2)  NOT NULL DEFAULT '0' COMMENT '诊断类型：0-默认(西医),1-中医',
  show_name      varchar(64)                           NOT NULL DEFAULT '' COMMENT '展示诊断名称',
  status         tinyint(2)  NOT NULL DEFAULT '0' null comment '状态 0启用 1禁用',
  sex_limit      tinyint(2)  NOT NULL DEFAULT '0' COMMENT '性别限制：0无限制,1限男,2限女',
  data_type      tinyint(2)  not null default 1 comment '数据类型：1-常规, 2-系统默认 3-推荐',
  creator        varchar(64) default ''                not null comment '创建者',
  create_time    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater        varchar(64) default '' null comment '更新者',
  update_time    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted        bit         default b'0'              not null comment '是否删除'
) comment '问诊诊断信息表';
CREATE INDEX idx_diagnosis_code ON saas_inquiry_diagnosis (diagnosis_code) comment '诊断编码索引';
CREATE INDEX idx_diagnosis_name ON saas_inquiry_diagnosis (diagnosis_name) comment '诊断名称索引';


#问诊主诉信息表
DROP TABLE IF EXISTS saas_inquiry_main_suit;
create table saas_inquiry_main_suit
(
  id             bigint auto_increment comment '主键' primary key,
  main_suit_name varchar(64)                           NOT NULL DEFAULT '' COMMENT '主诉名称',
  status         tinyint(2)                            NOT NULL DEFAULT '0' null comment '状态 0启用 1禁用',
  sex_limit      tinyint(2)                            NOT NULL DEFAULT '0' COMMENT '性别限制：0无限制,1限男,2限女',

  creator        varchar(64) default ''                not null comment '创建者',
  create_time    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater        varchar(64) default '' null comment '更新者',
  update_time    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted        bit         default b'0'              not null comment '是否删除'
) comment '问诊主诉信息表';
CREATE INDEX idx_main_suit_name ON saas_inquiry_main_suit (main_suit_name) comment '主诉名称索引';


#问诊用户
(医生药师核对调配发药) 签章信息表
DROP TABLE IF EXISTS saas_inquiry_user_signature_information;
create table saas_inquiry_user_signature_information
(
  id                 bigint auto_increment comment '主键' primary key,
  user_id            bigint                                 not null comment '用户id',
  tenant_id          bigint null comment '门店id,为空则当前数据作用全局',
  signature_platform tinyint(2)   default 0                 not null comment '签章平台 0-自签署 1-法大大',
  signature_biz_type tinyint(2)   default 1                 not null comment '签章业务类型 1-用户签名 2-授权合同',
  signature_task_id  varchar(64)  default ''                not null comment '签署任务id(eg:合同号)',
  signature_status   tinyint(2)   default 1                 not null comment '签章状态 1-签署中,2-签署完成',
  signature_url      varchar(512) default ''                not null comment '签署url(eg:合同链接/签名图片)',
  creator            varchar(64)  default ''                not null comment '创建者',
  create_time        datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater            varchar(64)  default '' null comment '更新者',
  update_time        datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted            bit          default b'0'              not null comment '是否删除'
) comment '问诊用户(医生药师核对调配发药)签章信息表';
CREATE INDEX idx_user_id_sign_platform_sign_biz_type ON saas_inquiry_user_signature_information (user_id, signature_platform, signature_biz_type) comment '用户平台业务类型索引';


#用户CA认证表
DROP TABLE IF EXISTS saas_inquiry_signature_ca_auth;
create table saas_inquiry_signature_ca_auth
(
  id                         bigint auto_increment comment '主键' primary key,
  user_id                    bigint                                not null comment '用户id',
  signature_platform         tinyint(2)  default 0                 not null comment '签章平台 0-自签署 1-法大大',

  certify_status             tinyint(2)  default 0                 not null comment '实名认证状态 0: 待认证，1: 认证完成，2: 认证失败',
  signature_status           tinyint(2)  default 0                 not null comment '签名状态 0: 未签名，1: 已签名',
  authorize_free_sign_status tinyint(2)  default 0                 not null comment '免签授权状态 0: 未授权，1: 已授权，',
  authorize_free_sign_ddl    datetime null comment '免签授权截止时间',
  authorize_agreement_status tinyint(2)  default 0                 not null comment '授权协议签署状态 0: 未签署，1: 已签署',
  part_time_agreement_status tinyint(2)  default 0                 not null comment '兼职协议状态 0: 未签署，1: 已签署',
  ext                        json null comment 'ca认证扩展信息-list',

  creator                    varchar(64) default ''                not null comment '创建者',
  create_time                datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater                    varchar(64) default '' null comment '更新者',
  update_time                datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted                    bit         default b'0'              not null comment '是否删除'
) comment '用户CA认证表';
CREATE INDEX idx_user_id ON saas_inquiry_signature_ca_auth (user_id) comment '用户id索引';

#签章平台用户表
DROP TABLE IF EXISTS saas_inquiry_signature_person;
CREATE TABLE saas_inquiry_signature_person
(
  id                 bigint      NOT NULL AUTO_INCREMENT COMMENT '自增主键' primary key,
  user_id            bigint      NOT NULL DEFAULT '' COMMENT '个人用户在应用中的唯一标识',
  open_user_id       varchar(64) NOT NULL DEFAULT '' COMMENT '平台为该用户在该应用appId范围内分配的唯一标识',
  signature_platform tinyint(2)           default 1 not null comment '签章平台  1-法大大',
  account_name       varchar(64) NOT NULL DEFAULT '' COMMENT '个人用户的法大大帐号，仅限手机号或邮箱',
  user_name          varchar(30) NOT NULL DEFAULT '' COMMENT '个人用户真实姓名',
  mobile             varchar(11) NOT NULL DEFAULT '' COMMENT '个人手机号',
  bank_account_no    varchar(32) NOT NULL DEFAULT '' COMMENT '个人银行账户号',
  user_ident_type    varchar(32) NOT NULL DEFAULT '' COMMENT '证件类型 ：id_card: 身份证;passport: 护照;hk_macao: 港澳居民来往内地通行证;taiwan: 台湾居民来往大陆通行证 ',
  user_ident_no      varchar(32) NOT NULL DEFAULT '' COMMENT '证件号。跟证件类型关联',
  seal_id            varchar(64) NOT NULL DEFAULT '' COMMENT '免签签名印章ID',
  user_status        tinyint(2)           default 0 NOT NULL COMMENT '0-未认证,1-已认证,3已设置签名',

  creator            varchar(64)          default '' not null comment '创建者',
  create_time        datetime             default CURRENT_TIMESTAMP not null comment '创建时间',
  updater            varchar(64)          default '' null comment '更新者',
  update_time        datetime             default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted            bit                  default b'0' not null comment '是否删除'
) COMMENT = '签章平台用户表';
CREATE UNIQUE INDEX uniq_user_id ON saas_inquiry_signature_person (user_id) comment '用户平台业务类型索引';

#三方签章平台

DROP TABLE IF EXISTS saas_inquiry_signature_platform;
create table saas_inquiry_signature_platform
(
  id                 bigint auto_increment comment '主键' primary key,
  signature_platform tinyint(2)   default 0                 not null comment '签章平台 0-自签署 1-法大大',
  master             bit                                    not null comment '是否为主配置',
  param_name         varchar(512) default ''                not null comment '属性名 eg:私有云地址 对应枚举',
  param_value        varchar(512) default ''                not null comment '属性值 eg:8000809',
  description        varchar(512) default ''                not null comment '描述',
  ext                text null comment '扩展信息',
  creator            varchar(64)  default ''                not null comment '创建者',
  create_time        datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater            varchar(64)  default '' null comment '更新者',
  update_time        datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted            bit          default b'0'              not null comment '是否删除'
) comment '三方签章平台配置表';
CREATE INDEX idx_signature_platform_param_name ON saas_inquiry_signature_platform (signature_platform, param_name) comment '属性名索引';


#三方签章平台回调日志表
DROP TABLE IF EXISTS saas_inquiry_signature_callback_log;
CREATE TABLE saas_inquiry_signature_callback_log
(
  id                 bigint      NOT NULL AUTO_INCREMENT COMMENT '自增主键' primary key,
  signature_platform tinyint(2)           default 0 not null comment '签章平台 0-自签署 1-法大大',
  type               varchar(64) NOT NULL DEFAULT '' COMMENT '类型：事件ID',
  biz_id             varchar(64) NOT NULL DEFAULT '' COMMENT '业务id',
  biz_content        json                 DEFAULT NULL COMMENT '具体事件的请求参数，json字符串',
  create_time        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  deleted            bit                  default b'0' not null comment '是否删除'
) COMMENT = '三方签章平台回调日志表';
CREATE INDEX idx_time_platform_type_biz ON saas_inquiry_signature_callback_log (create_time, signature_platform, type, biz_id) comment '属性名索引';


#签署合同表
DROP TABLE IF EXISTS saas_inquiry_signature_contract;
CREATE TABLE saas_inquiry_signature_contract
(
  id                 bigint       NOT NULL AUTO_INCREMENT COMMENT '主键' primary key,
  pref               varchar(64)  NOT NULL DEFAULT '' COMMENT '合同编号,系统生成',
  signature_platform tinyint(2)            default 0 not null comment '签章平台  0-自绘,1-法大大',
  contract_status    tinyint(2)   NOT NULL DEFAULT 0 COMMENT '合同状态',
  self_drawn         tinyint(2)            default 1 not null comment '自绘合同标识  0-是,1-否',
  self_drawn_status  tinyint(2)            default 0 not null comment '自绘合同状态',
  contract_type      tinyint(2)   NOT NULL DEFAULT 1 COMMENT '合同业务类型',
  biz_id             varchar(64)  NOT NULL DEFAULT '' COMMENT '业务方唯一标识',
  third_id           varchar(64)  NOT NULL DEFAULT '' COMMENT '三方签署任务id signTaskId',
  initiator_user_id  bigint       NOT NULL DEFAULT 0 NULL COMMENT '发起方userId',
  initiator_name     varchar(32)  NOT NULL DEFAULT '' COMMENT '发起方姓名',
  initiator_mobile   varchar(32)  NOT NULL DEFAULT '' COMMENT '发起方联系方式',
  participants       text NULL COMMENT '参与方集合',
  param_detail       text NULL COMMENT '合同参数详情',
  img_url            varchar(512) NOT NULL DEFAULT '' COMMENT '合同图片',
  pdf_url            varchar(512) NOT NULL DEFAULT '' COMMENT '合同PDF文件',
  ext                json null comment '扩展信息',
  creator            varchar(64)           default '' not null comment '创建者',
  create_time        datetime              default CURRENT_TIMESTAMP not null comment '创建时间',
  updater            varchar(64)           default '' null comment '更新者',
  update_time        datetime              default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted            bit                   default b'0' not null comment '是否删除'
) comment '签章合同表';
CREATE INDEX idx_third_id ON saas_inquiry_signature_contract (third_id) comment '三方id索引';
CREATE INDEX idx_biz_id ON saas_inquiry_signature_contract (biz_id) comment '业务id索引';
CREATE INDEX idx_time_platform_type_biz_no ON saas_inquiry_signature_contract (create_time, signature_platform, contract_type, contract_no) comment '时间组合索引';


-- 医院参数配置表
drop table if exists saas_inquiry_hospital_setting;
create table saas_inquiry_hospital_setting
(
  id          bigint auto_increment comment '主键'
    primary key,
  hospital_id bigint                                 not null comment '医院id',
  param_type  int          default 0                 not null comment '参数类型 eg:1 默认处方笺模版（西医）',
  param_name  varchar(512) default ''                not null comment '参数类型 eg:1 默认处方笺模版（西医）',
  param_value varchar(512) default '' null comment '值 eg:1 模板id',
  description varchar(512) default ''                not null comment '参数描述',
  ext         json null comment '配置拓展字段',
  creator     varchar(64)  default ''                not null comment '创建者',
  create_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater     varchar(64)  default '' null comment '更新者',
  update_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted     bit          default b'0'              not null comment '是否删除'
) comment '医院参数配置表';
create unique index udx_hospital_id_param_type
  on saas_inquiry_hospital_setting (hospital_id, param_type);


-- 诊断关联科室
drop table if exists saas_inquiry_diagnosis_department_relation;
create table saas_inquiry_diagnosis_department_relation
(
  id             bigint auto_increment comment '主键'
    primary key,
  diagnosis_code varchar(64) default ''                not null comment '诊断编码',
  diagnosis_name varchar(64) default ''                not null comment '诊断名称',
  dept_id        bigint                                not null comment '医院科室id',
  dept_pref      varchar(64) default ''                not null comment '科室编码,eg:101',
  dept_name      varchar(64) default ''                not null comment '科室名称,eg:内科',
  creator        varchar(64) default ''                not null comment '创建者',
  create_time    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater        varchar(64) default '' null comment '更新者',
  update_time    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted        bit         default b'0'              not null comment '是否删除'
) comment '科室诊断关联表';

create unique index udx_diagnosis_code_dept_pref
  on saas_inquiry_diagnosis_department_relation (diagnosis_code, dept_pref);
create index idx_dept_id_diagnosis_code
  on saas_inquiry_diagnosis_department_relation (dept_pref, diagnosis_code);


-- 全局/区域/门店 问诊配置选项
drop table if exists saas_inquiry_option_config;
create table saas_inquiry_option_config
(
  id            bigint auto_increment comment 'ID'
    primary key,
  target_type   tinyint      default 0                 not null comment '目标类型 1 全局 2 区域 3 门店',
  option_type   int          default 0                 not null comment '配置选项类型 1 图文问诊 2 诊断科室',
  target_id     varchar(64)  default ''                not null comment '目标编号(区域编码或者租户id)',
  target_name   varchar(64)  default ''                not null comment '目标名称(区域名称或者租户名称)',
  province      varchar(16)  default ''                not null comment '省',
  province_code varchar(16)  default ''                not null comment '省编码',
  city          varchar(16)  default ''                not null comment '市',
  city_code     varchar(16)  default ''                not null comment '市编码',
  area          varchar(16)  default ''                not null comment '区',
  area_code     varchar(16)  default ''                not null comment '区编码',
  option_name   varchar(512) default ''                not null comment '配置选项类型',
  option_value  varchar(512) default '' null comment '配置值',
  description   varchar(512) default ''                not null comment '配置描述',
  ext           json null comment '处方拓展字段',
  creator       varchar(64)  default ''                not null comment '创建人',
  create_time   datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater       varchar(64)  default ''                not null comment '更新者',
  update_time   datetime     default CURRENT_TIMESTAMP not null comment '更新时间',
  deleted       bit          default b'0'              not null comment '是否删除'
) comment '问诊配置选项';

create unique index udx_type_tid_opt_type
  on saas_inquiry_option_config (target_type, target_id, option_type);
create index idx_type_opt_type_tid
  on saas_inquiry_option_config (target_type, option_type, target_id);
create index idx_type_tname
  on saas_inquiry_option_config (target_type, option_type, target_name);
create index idx_type_code
  on saas_inquiry_option_config (target_type, option_type, province_code, city_code, area_code);


#
腾讯IM用户表
DROP TABLE IF EXISTS saas_inquiry_im_user;
CREATE TABLE saas_inquiry_im_user
(
  id               bigint                                NOT NULL AUTO_INCREMENT COMMENT '主键' primary key,
  user_id          bigint                                NOT NULL COMMENT '用户id',
  account_id       varchar(64)                           NOT NULL COMMENT 'im用户名,唯一',
  nick_name        varchar(128)                          NOT NULL DEFAULT '' COMMENT '用户昵称',
  client_type      tinyint(2)                            NOT NULL DEFAULT 0 COMMENT '客户端类型 0-app端  1-web端',
  last_device_type tinyint(2)                       NOT NULL DEFAULT 0 COMMENT '最后一次使用设备类型 1-ios  2-android  3-harmony',
  user_status      tinyint(2)                            NOT NULL DEFAULT 0 COMMENT '用户状态 0-正常 1-禁用',
  creator          varchar(64) default ''                not null comment '创建者',
  create_time      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater          varchar(64) default '' null comment '更新者',
  update_time      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted          bit         default b'0'              not null comment '是否删除'
) comment '腾讯IM用户表';
CREATE INDEX idx_user_id ON saas_inquiry_im_user (user_id) comment '用户id索引';
CREATE INDEX idx_user_account_id ON saas_inquiry_im_user (account_id) comment 'im用户名索引';


#
腾讯IM用户消息表
DROP TABLE IF EXISTS saas_inquiry_im_message;
CREATE TABLE saas_inquiry_im_message
(
  id           bigint                                NOT NULL AUTO_INCREMENT COMMENT '主键' primary key,
  msg_key      varchar(64)                           NOT NULL COMMENT '消息唯一key',
  msg_seq      bigint(32)                            NOT NULL COMMENT '消息序列号-发送时间相同的情况下按MsgSeq 排序，MsgSeq 值越大消息越靠后',
  inquiry_pref varchar(64)                           NOT NULL COMMENT '问诊单号',
  msg_from     varchar(64)                           NOT NULL COMMENT '发送方IM用户名',
  msg_to       varchar(64)                           NOT NULL COMMENT '接收方IM用户名',
  msg_time     datetime(3)                           NOT NULL COMMENT '消息发送时间',
  read_offset  tinyint(2)  default 0                 NOT NULL COMMENT '消息已读位点 0-未读 1-已读',
  msg_body     text                                  NOT NULL COMMENT '消息体内容',
  msg_ext      text                                  NOT NULL COMMENT '消息扩展内容',

  creator      varchar(64) default ''                not null comment '创建者',
  create_time  datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater      varchar(64) default '' null comment '更新者',
  update_time  datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted      bit         default b'0'              not null comment '是否删除'
) comment '腾讯IM用户消息表';
CREATE INDEX idx_inquiry_pref_msgtime ON saas_inquiry_im_message (inquiry_pref, msg_time, msg_seq) comment '问诊单号索引';

#
视频转码记录表
DROP TABLE IF EXISTS saas_inquiry_transcoding;
CREATE TABLE saas_inquiry_transcoding
(
  id           bigint                                 NOT NULL AUTO_INCREMENT COMMENT '主键' primary key,
  inquiry_pref varchar(64)                            NOT NULL COMMENT '问诊单号',
  task_id      varchar(128) default ''                NOT NULL COMMENT '转码任务id',
  status       tinyint(2)   default 0                 NOT NULL COMMENT '转码状态 0-待转码  1-未查询到文件id  2-开启转码失败  3-已开启转码  4-未查询到转码结果',
  error_info   varchar(512) default ''                NOT NULL COMMENT '当前调腾讯失败原因，格式 code-errmsg',

  creator      varchar(64)  default ''                not null comment '创建者',
  create_time  datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater      varchar(64)  default '' null comment '更新者',
  update_time  datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted      bit          default b'0'              not null comment '是否删除'
) comment '问诊视频转码记录表';
CREATE INDEX idx_inquiry_pref ON saas_inquiry_transcoding (inquiry_pref) comment '问诊单号索引';


#
外配(电子)处方记录表
DROP TABLE IF EXISTS saas_prescription_external;
CREATE TABLE saas_prescription_external
(
  id                            bigint auto_increment comment '主键' primary key,
  pref                          varchar(64)  NOT NULL DEFAULT '' COMMENT '外配处方编号',
  biz_id                        varchar(64)  NOT NULL COMMENT '业务id',
  biz_type                      tinyint(2)   not null DEFAULT 0 comment '业务类型 0-问诊,1-智慧脸...',
  tenant_id                     bigint(20)   NOT NULL COMMENT '门店ID',
  biz_channel_type              int(11)   NOT NULL DEFAULT 0 COMMENT '三方业务渠道 0荷叶,1智慧脸 2海典erp 3众康云',
  external_type                 tinyint(2)   NOT NULL DEFAULT 0 comment '外配处方类型 0.电子处方、1.纸质处方',
  external_rx_pref              varchar(255) NOT NULL DEFAULT '' COMMENT '外部系统处方号',

  fix_medical_institutions_code varchar(32)  NOT NULL DEFAULT '' COMMENT '定点医疗机构编号',
  fix_medical_institutions_name varchar(32)  NOT NULL DEFAULT '' COMMENT '定点医疗机构名称',
  rx_out_time                   datetime COMMENT '开方时间',
  rx_expire_time                datetime COMMENT '有效截止时间',
  dept_pref                     varchar(64)  NOT NULL DEFAULT '' COMMENT '科室编码',
  dept_name                     varchar(64)  NOT NULL DEFAULT '' COMMENT '科室名称',
  doctor_pref                   varchar(64)  NOT NULL DEFAULT '' COMMENT '医生编号',
  doctor_name                   varchar(32)  NOT NULL DEFAULT '' COMMENT '医生姓名',
  pharmacist_pref               varchar(64)  NOT NULL DEFAULT '' COMMENT '药师编码',
  pharmacist_name               varchar(32)  NOT NULL DEFAULT '' COMMENT '药师姓名',
  rx_category                   int(6)       NULL comment '处方类别',
  long_term                     tinyint(2)   NULL comment '长期处方标识',

  electronic_rx_sn              varchar(64) NULL     DEFAULT '' COMMENT '电子处方平台流水号',
  out_flow_status               tinyint(2)   NULL     DEFAULT '0' COMMENT '电子处方外流状态 0待外流',
  rx_sign_verify_sn             varchar(64) NULL     DEFAULT '' COMMENT '电子处方签名验签流水号',
  rx_chk_biz_sn                 varchar(64) NULL     DEFAULT '' COMMENT '电子处方审核业务流水号',
  rx_chk_status                 tinyint(2)   NULL COMMENT '电子处方审核状态',
  rx_chk_time                   datetime COMMENT '电子处方审核时间',
  medicare_rx_no                varchar(64) NULL     DEFAULT '' COMMENT '医保处方号',
  medicare_rx_trace_code        varchar(64) NULL     DEFAULT '' COMMENT '医保处方追溯码',
  medicare_rx_status            tinyint(2)   NULL     DEFAULT '0' COMMENT '医保处方状态 0初始,1有效 2失效',
  ext                           text COMMENT '外配处方扩展字段',

  creator                       varchar(64)           default '' not null comment '创建者',
  create_time                   datetime              default CURRENT_TIMESTAMP not null comment '创建时间',
  updater                       varchar(64)           default '' null comment '更新者',
  update_time                   datetime              default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted                       bit                   default b'0' not null comment '是否删除'
) comment '外配(电子)处方记录表';
create index idx_pref_biz_type on saas_prescription_external (pref, biz_type) comment '编号+类型索引';
create index idx_tenant_id_biz_id_type on saas_prescription_external (tenant_id, biz_id, biz_type) comment '门店业务索引';
create index idx_external_rx_pref_type on saas_prescription_external (external_rx_pref, biz_type) comment '外部处方号索引';


#
医保订单信息表
DROP TABLE IF EXISTS saas_medical_insurance_order;
CREATE TABLE saas_medical_insurance_order
(
  id                            bigint auto_increment comment '主键' primary key,
  pref                          varchar(64)    NOT NULL DEFAULT '' COMMENT '医保订单编号',
  biz_id                        varchar(64)    NOT NULL COMMENT '业务id',
  biz_type                      tinyint(2)     not null DEFAULT 1 comment '业务类型 0-问诊,1-智慧脸...',
  tenant_id                     bigint(20)     NOT NULL COMMENT '门店ID',

  fix_medical_institutions_code varchar(32)    NOT NULL DEFAULT '' COMMENT '定点机构编号',
  setl_id                       varchar(64)    NOT NULL DEFAULT '' COMMENT '结算ID',
  medical_id                    varchar(64)    NOT NULL DEFAULT '' COMMENT '就诊ID',
  psn_no                        varchar(64)    NOT NULL DEFAULT '' COMMENT '人员编号',
  psn_name                      varchar(64)    NOT NULL DEFAULT '' COMMENT '人员姓名',
  psn_cert_type                 varchar(64)    NOT NULL DEFAULT '' COMMENT '人员证件类型',
  cert_no                       varchar(64)    NOT NULL DEFAULT '' COMMENT '证件号码',
  sex                           tinyint(2)     NOT NULL DEFAULT 1 COMMENT '性别：1 男 2 女',
  age                           varchar(3)     NOT NULL DEFAULT '' COMMENT '年龄',
  insurance_type                varchar(6)     NOT NULL DEFAULT '' COMMENT '险种类型',
  psn_type                      varchar(6)     NOT NULL DEFAULT '' COMMENT '人员类别',
  cvl_serv_flag                 varchar(3)     NOT NULL DEFAULT '' COMMENT '公务员标志',
  setl_time                     datetime NULL COMMENT '结算时间',
  medical_cert_type             varchar(6)     NOT NULL DEFAULT '' COMMENT '就诊凭证类型',
  med_type                      varchar(6)     NOT NULL DEFAULT '' COMMENT '医疗类别',

  medical_institutions_setl_id  varchar(64)    NOT NULL DEFAULT '' COMMENT '医药机构结算ID',
  clr_options                   varchar(12)    NOT NULL DEFAULT '' COMMENT '清算经办机构',
  clr_way                       varchar(12)    NOT NULL DEFAULT '' COMMENT '清算方式',
  clr_type                      varchar(12)    NOT NULL DEFAULT '' COMMENT '清算类别',

  balance                       decimal(16, 2) NOT NULL DEFAULT '0.000' COMMENT '本次交易后的账户余额',
  medical_fee_sum_amt           decimal(16, 2) NOT NULL DEFAULT '0.000' COMMENT '医疗费总额',
  psn_part_amt                  decimal(16, 2) NOT NULL DEFAULT '0.000' COMMENT '个人负担总金额',
  fund_pay_sum_amt              decimal(16, 2) NOT NULL DEFAULT '0.000' COMMENT '基金支付总额',
  oth_pay                       decimal(16, 2) NOT NULL DEFAULT '0.000' COMMENT '其他支出',
  hosp_part_amt                 decimal(16, 2) NOT NULL DEFAULT '0.000' COMMENT '医院负担金额',

  acct_pay                      decimal(16, 2) NOT NULL DEFAULT '0.000' COMMENT '个人账户支出',
  psn_cash_pay                  decimal(16, 2) NOT NULL DEFAULT '0.000' COMMENT '个人现金支出',
  acct_mutual_aid_pay           decimal(16, 2) NOT NULL DEFAULT '0.000' COMMENT '个人账户共济支付金额',

  ext                           text COMMENT '医保订单扩展字段',

  creator                       varchar(64)             default '' not null comment '创建者',
  create_time                   datetime                default CURRENT_TIMESTAMP not null comment '创建时间',
  updater                       varchar(64)             default '' null comment '更新者',
  update_time                   datetime                default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted                       bit                     default b'0' not null comment '是否删除'
) comment '医保订单信息表';
create index idx_pref on saas_medical_insurance_order (pref) comment '编号索引';
create index idx_biz_id_type on saas_medical_insurance_order (biz_id, biz_type) comment '业务+类型索引';
create index idx_tenant_id_biz_id_type on saas_medical_insurance_order (tenant_id, biz_id, biz_type) comment '门店业务索引';

#三方平台预问诊
（
处方
）表
DROP TABLE IF EXISTS saas_third_party_pre_inquiry;
CREATE TABLE `saas_third_party_pre_inquiry`
(
  `id`                    bigint(20)  AUTO_INCREMENT PRIMARY KEY COMMENT 'ID',
  `pref`                  varchar(64) NOT NULL DEFAULT '' COMMENT '预问诊编号',
  `transmission_organ_id` int(11) NOT NULL COMMENT '三方平台标识',
  `tenant_id`             bigint(20) NOT NULL COMMENT '租户ID',
  `tenant_name`           varchar(64) NOT NULL DEFAULT '' COMMENT '冗余-租户名称',
  `inquiry_pref`          varchar(64)          DEFAULT '' COMMENT '问诊单pref',
  `user_name`             varchar(20)          DEFAULT NULL COMMENT '用户名称',
  `mobile`                varchar(20)          DEFAULT NULL COMMENT '手机号',
  `id_card`               varchar(32)          DEFAULT NULL COMMENT '身份证',
  `age`                   int(11) DEFAULT NULL COMMENT '年龄',
  `sex`                   int(11) DEFAULT NULL COMMENT '性别',
  `medicine_type`         int(11) DEFAULT NULL COMMENT '用药类型：0西药，1中药',
  `inquiry_way_type`      tinyint(2)           COMMENT '开方方式：1图文 2 视频  3电话',
  `audit_status`          tinyint(2)           DEFAULT 0  NOT NULL COMMENT '审核状态 0-待审核  1-审核通过  2-审核驳回',
  `ext`                   text COMMENT '扩展字段',

  `creator`               varchar(64) NOT NULL DEFAULT '' COMMENT '创建者',
  `create_time`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater`               varchar(64)          DEFAULT '' COMMENT '更新者',
  `update_time`           datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted`               bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='三方平台预问诊（处方）表';
create index idx_tenant_id on saas_third_party_pre_inquiry (tenant_id) comment '门店索引';
create index idx_inquiry_pref on saas_third_party_pre_inquiry (inquiry_pref) comment '问诊单号索引';


#三方平台预问诊
（
处方
）明细表
DROP TABLE IF EXISTS saas_third_party_pre_inquiry_detail;
CREATE TABLE `saas_third_party_pre_inquiry_detail`
(
  `id`                         bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `tenant_id`                  bigint(20) NOT NULL DEFAULT '0' COMMENT '租户id',
  `third_party_pre_inquiry_id` bigint(20) NOT NULL COMMENT '预问诊表id',
  `common_name`                varchar(255)            DEFAULT NULL COMMENT '药品名称',
  `package_unit`               varchar(255)            DEFAULT NULL COMMENT '包装单位名称',
  `attribute_specification`    varchar(255)            DEFAULT NULL COMMENT '商品规格',
  `quantity`                   decimal(10, 3) NOT NULL DEFAULT '0.000' COMMENT '数量',
  `bar_code`                   varchar(255)            DEFAULT NULL COMMENT '69码',
  `manufacturer`               varchar(255)            DEFAULT NULL COMMENT '生产厂家',
  `approval_number`            varchar(255)            DEFAULT NULL COMMENT '批准文号',
  `creator`                    varchar(64)    NOT NULL DEFAULT '' COMMENT '创建者',
  `create_time`                datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater`                    varchar(64)             DEFAULT '' COMMENT '更新者',
  `update_time`                datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted`                    bit(1)         NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY                          `idx_third_party_pre_inquiry_id` (`third_party_pre_inquiry_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1916728186061684738 DEFAULT CHARSET=utf8mb4 COMMENT='三方平台预问诊（处方）药品表';

#
三方药品匹配失败记录
DROP TABLE IF EXISTS saas_third_party_drug_match_fail_record;
CREATE TABLE `saas_third_party_drug_match_fail_record`
(
  `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `tenant_id`               bigint(20) NOT NULL COMMENT '租户ID',
  `biz_id`                  varchar(100)         DEFAULT NULL COMMENT '业务id',
  `transmission_organ_id`   int(11) NOT NULL COMMENT '三方平台代码',
  `common_name`             varchar(255)         DEFAULT NULL COMMENT '药品名称',
  `attribute_specification` varchar(128)         DEFAULT NULL COMMENT '商品规格',
  `bar_code`                varchar(128)         DEFAULT NULL COMMENT '69码',
  `approval_number`         varchar(128)         DEFAULT NULL COMMENT '批准文号',
  `match_fail_msg`          varchar(256)         DEFAULT NULL COMMENT '匹配失败原因',
  `creator`                 varchar(64) NOT NULL DEFAULT '' COMMENT '创建者',
  `create_time`             datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater`                 varchar(64)          DEFAULT '' COMMENT '更新者',
  `update_time`             datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted`                 bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY                       `idx_tenant_id` (`tenant_id`) USING BTREE,
  KEY                       `idx_transmission_organ_id` (`transmission_organ_id`) USING BTREE,
  KEY                       `idx_common_name` (`common_name`) USING BTREE,
  KEY                       `idx_create_time` (`create_time`) USING BTREE
) COMMENT='三方药品匹配失败记录';

-- 医院员工关系表
DROP TABLE IF EXISTS saas_inquiry_hospital_employee;
CREATE TABLE `saas_inquiry_hospital_employee`
(
  `id`            bigint       NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `hospital_pref` varchar(64)  NOT NULL DEFAULT '' COMMENT '互联网医院编号',
  `hospital_name` varchar(100) NOT NULL COMMENT '医院名称',
  `user_id`       bigint       NOT NULL COMMENT '用户ID',
  `bind_status`   tinyint      NOT NULL DEFAULT 0 COMMENT '帐号状态（0绑定 1解绑）',
  `creator`       varchar(64)           DEFAULT '' COMMENT '创建者',
  `create_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater`       varchar(64)           DEFAULT '' COMMENT '更新者',
  `update_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted`       bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  KEY             `idx_hospital_id` (`hospital_pref`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医院员工绑定关系表';

#
医院/药店签到记录表
DROP TABLE IF EXISTS saas_medicare_signin_record;
CREATE TABLE saas_medicare_signin_record
(
  id                        bigint auto_increment comment '主键' primary key,
  institution_type          tinyint(2)  NOT NULL DEFAULT 1 COMMENT '两定机构类型 1-医院 2-药店',
  institution_code          varchar(64) NOT NULL DEFAULT '' COMMENT '两定机构编码',
  medicare_institution_code varchar(64) NOT NULL DEFAULT '' COMMENT '医药机构编码',
  operator_name             varchar(32) NOT NULL DEFAULT '' COMMENT '操作员姓名',
  operator_code             varchar(32) NOT NULL DEFAULT '' COMMENT '操作员编码',
  sign_no                   varchar(64) NOT NULL DEFAULT '' COMMENT '签到号',
  signin_status             tinyint(2)  NOT NULL DEFAULT 0 COMMENT '签到状态 0-未签到 1-已签到 2-已签退',
  signin_time               datetime NULL COMMENT '签到时间',
  signout_time              datetime NULL COMMENT '签退时间',
  tenant_id                 bigint(20)  NOT NULL DEFAULT 0 COMMENT '租户ID',
  creator                   varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  create_time               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater                   varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted                   bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) COMMENT ='医院/药店签到记录表';

create index idx_institution_code_status on saas_medicare_signin_record (medicare_institution_code, signin_status) comment '机构编码+签到状态索引';
create index idx_tenant_id_status on saas_medicare_signin_record (tenant_id, signin_status) comment '租户+签到状态索引';


-- 参保信息记录表
DROP TABLE IF EXISTS saas_medicare_person_insurance_record;
CREATE TABLE saas_medicare_person_insurance_record
(
  id             bigint auto_increment comment '主键' primary key,
  tenant_id      bigint(20)   NOT NULL COMMENT '租户ID',
  psn_no         varchar(32)  NOT NULL DEFAULT '' COMMENT '人员编号',
  psn_name       varchar(50)  NOT NULL DEFAULT '' COMMENT '人员姓名',
  certno         varchar(32)  NOT NULL DEFAULT '' COMMENT '证件号码',
  gend           varchar(10)  NOT NULL DEFAULT '' COMMENT '性别',
  age            varchar(10)  NOT NULL DEFAULT '' COMMENT '年龄',
  brdy           varchar(20)  NOT NULL DEFAULT '' COMMENT '出生日期',
  insutype       varchar(10)  NOT NULL DEFAULT '' COMMENT '险种类型',
  insuplc_admdvs varchar(20)  NOT NULL DEFAULT '' COMMENT '参保地行政区划',
  emp_name       varchar(128) NOT NULL DEFAULT '' COMMENT '参保单位名称',
  psn_type       varchar(10)  NOT NULL DEFAULT '' COMMENT '参保人员类别',
  psn_insu_stas  varchar(10)  NOT NULL DEFAULT '' COMMENT '参保人员状态',
  psn_insu_date  varchar(20)  NOT NULL DEFAULT '' COMMENT '参保开始时间',
  creator        varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人',
  create_time    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater        varchar(64)  NOT NULL DEFAULT '' COMMENT '更新者',
  update_time    datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted        bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除'
) COMMENT ='参保信息记录表';

-- 创建索引
CREATE INDEX idx_certno ON saas_medicare_person_insurance_record (certno) COMMENT '身份证号索引';
CREATE INDEX idx_psn_no ON saas_medicare_person_insurance_record (psn_no) COMMENT '人员编号索引';
CREATE INDEX idx_tenant_id ON saas_medicare_person_insurance_record (tenant_id) COMMENT '租户ID索引';


----------------------------------------------------------合理用药 -----------------------------------------------------------
DROP TABLE IF EXISTS inquiry_rational_category_detail;
CREATE TABLE `inquiry_rational_category_detail`
(
  id          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  category_id int(11) DEFAULT NULL COMMENT '类别字典id',
  classify_id int(11) DEFAULT NULL COMMENT '中台分类末级id',
  yn          tinyint(4) NOT NULL DEFAULT '0' COMMENT '有效：0是，1否 ',
  creator     varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  create_time datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater     varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted     bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 27667 DEFAULT CHARSET = utf8mb4 COMMENT = '问诊类别分类详情表'
CREATE INDEX idx_category_id ON inquiry_rational_category_detail (category_id) COMMENT '类别id索引';
CREATE INDEX idx_classify_id ON inquiry_rational_category_detail (classify_id) COMMENT '中台末级分类id索引';


DROP TABLE IF EXISTS inquiry_rational_compatibility;
CREATE TABLE `inquiry_rational_compatibility`
(
  id                     int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  pref                   varchar(32) NOT NULL DEFAULT '' COMMENT 'PW编码 PW1000开始',
  caution                tinyint(4) NOT NULL DEFAULT '0' COMMENT '提示级别：0默认空不限,1提醒,2慎用,3忌用,4禁用',
  description            varchar(512)         DEFAULT NULL COMMENT '作用说明',
  categories_first       varchar(1024)        DEFAULT NULL COMMENT 'A类别id集合用逗号分割',
  common_products_first  text COMMENT 'A通用名id集合用逗号分割',
  category_second        varchar(512)         DEFAULT NULL COMMENT 'B类别id集合用逗号分割',
  common_products_second text COMMENT 'B通用名id集合用逗号分割',
  status                 tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0启用，1禁用 ',
  yn                     tinyint(4) NOT NULL DEFAULT '0' COMMENT '有效：0是，1否 ',
  creator                varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  create_time            datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater                varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time            datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted                bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 28 DEFAULT CHARSET = utf8mb4 COMMENT = '问诊配伍禁忌主表'


DROP TABLE IF EXISTS inquiry_rational_compatibility_detail;
CREATE TABLE `inquiry_rational_compatibility_detail`
(
  id           int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  pref         varchar(32) NOT NULL DEFAULT '' COMMENT 'PW编码',
  comp_id      int(11) DEFAULT NULL COMMENT '配伍禁忌主表id',
  first_type   tinyint(4) NOT NULL DEFAULT '1' COMMENT 'A类型 1类别,2通用名',
  first_param  varchar(64)          DEFAULT NULL COMMENT 'A id',
  second_type  tinyint(4) NOT NULL DEFAULT '1' COMMENT 'B类型 1类别,2通用名',
  second_param varchar(64)          DEFAULT NULL COMMENT 'B id ',
  yn           tinyint(4) NOT NULL DEFAULT '0' COMMENT '有效：0是，1否 ',
  creator      varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  create_time  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater      varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted      bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 321 DEFAULT CHARSET = utf8mb4 COMMENT = '问诊配伍禁忌明细表'
CREATE INDEX idx_first_type_param ON inquiry_rational_compatibility_detail (first_type, first_param) COMMENT '类别索引';


DROP TABLE IF EXISTS inquiry_rational_diagnosis;
CREATE TABLE `inquiry_rational_diagnosis`
(
  id              int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  diagnosis_pref  varchar(32) NOT NULL DEFAULT '' COMMENT 'ZD编码 ZD1000开始',
  caution         tinyint(4) NOT NULL DEFAULT '0' COMMENT '提示级别：0默认空不限,1提醒,2慎用,3忌用,4禁用',
  description     varchar(512)         DEFAULT NULL COMMENT '作用说明',
  diagnosis_codes varchar(1024)        DEFAULT NULL COMMENT '诊断codes集合用逗号分割',
  categories      varchar(1024)        DEFAULT NULL COMMENT '类别id集合用逗号分割',
  common_products text COMMENT '通用名称集合用逗号分割',
  status          tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0启用，1禁用 ',
  yn              tinyint(4) NOT NULL DEFAULT '0' COMMENT '有效：0是，1否 ',
  creator         varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  create_time     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater         varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time     datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted         bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 10 DEFAULT CHARSET = utf8mb4 COMMENT = '问诊诊断禁忌表'



DROP TABLE IF EXISTS inquiry_rational_dict_config;
CREATE TABLE `inquiry_rational_dict_config`
(
  id          int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  type        int(4) NOT NULL DEFAULT '0' COMMENT '业务id 相同的id 为统一业务，代码枚举',
  name        varchar(255) NOT NULL DEFAULT '' COMMENT '名称 eg:新生儿/抗生素',
  value       varchar(255) NOT NULL DEFAULT '' COMMENT '值,eg:1 / 规则解析表达式(单位:天) 0,365',
  description varchar(255) NOT NULL DEFAULT '' COMMENT '描述(说明) eg: X<28天',
  sys_type    tinyint(4) NOT NULL DEFAULT '1' COMMENT '字典类型：1-默认, 2-系统字典 3-推荐字典',
  status      tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0启用，1禁用 ',
  yn          tinyint(4) NOT NULL DEFAULT '0' COMMENT '有效：0是，1否 ',
  creator     varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人',
  create_time datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater     varchar(64)  NOT NULL DEFAULT '' COMMENT '更新者',
  update_time datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted     bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 15139 DEFAULT CHARSET = utf8mb4 COMMENT = '问诊限制类规则字典配置表';
CREATE INDEX idx_type_name ON inquiry_rational_dict_config (type, name) COMMENT '业务类型+名称索引';


DROP TABLE IF EXISTS inquiry_rational_product;
CREATE TABLE `inquiry_rational_product`
(
  id              int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  common_name     varchar(255) NOT NULL COMMENT '商品通用名称',
  category_lv6_id varchar(64)           DEFAULT NULL COMMENT '6级分类',
  medicare_remark varchar(1024)         DEFAULT '' COMMENT '医保提醒',
  status          tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0启用，1禁用 ',
  yn              tinyint(4) NOT NULL DEFAULT '0' COMMENT '有效：0是，1否 ',
  creator         varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人',
  create_time     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater         varchar(64)  NOT NULL DEFAULT '' COMMENT '更新者',
  update_time     datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted         bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 22355 DEFAULT CHARSET = utf8mb4 COMMENT = '限制类药品表';
CREATE INDEX idx_common_name ON inquiry_rational_product (common_name) COMMENT '通用名索引';


DROP TABLE IF EXISTS inquiry_rational_product_rule;
CREATE TABLE `inquiry_rational_product_rule`
(
  id                int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  product_common_id int(11) DEFAULT NULL COMMENT 'id',
  limit_drug_type   tinyint(12) DEFAULT NULL COMMENT '限制类型 eg:1性别2年龄',
  param             text COMMENT '参数或者表达式',
  caution           tinyint(4) NOT NULL DEFAULT '0' COMMENT '提示级别：0默认空不限,1提醒,2慎用,3忌用,4禁用',
  status            tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0启用，1禁用 ',
  yn                tinyint(4) NOT NULL DEFAULT '0' COMMENT '有效：0是，1否 ',
  creator           varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  create_time       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater           varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time       datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted           bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 255476 DEFAULT CHARSET = utf8mb4 COMMENT = '药品限制规则表';
CREATE INDEX idx_product_common_id ON inquiry_rational_product_rule (product_common_id) COMMENT '限制类药品索引';

DROP TABLE IF EXISTS inquiry_repeat_use_drug_limit;
CREATE TABLE `inquiry_repeat_use_drug_limit`
(
  id          bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  status      tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0启用，1禁用 ',
  yn          tinyint(4) NOT NULL DEFAULT '0' COMMENT '有效：0是，1否 ',
  creator     varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  create_time datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater     varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted     bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 118 DEFAULT CHARSET = utf8mb4 COMMENT = '重复用药限制表'


DROP TABLE IF EXISTS inquiry_repeat_use_drug_limit_detail;
CREATE TABLE `inquiry_repeat_use_drug_limit_detail`
(
  id                       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  repeat_use_drug_limit_id bigint(20) DEFAULT NULL COMMENT '重复用药限制表主键',
  classify_id              int(11) DEFAULT NULL COMMENT '中台分类末级id',
  yn                       tinyint(4) NOT NULL DEFAULT '0' COMMENT '有效：0是，1否 ',
  creator                  varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  create_time              datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater                  varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time              datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted                  bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 639 DEFAULT CHARSET = utf8mb4 COMMENT = '重复用药限制详情表';
CREATE INDEX idx_category_id ON inquiry_repeat_use_drug_limit_detail (repeat_use_drug_limit_id) COMMENT '重复用药商品索引';
CREATE INDEX idx_classify_id ON inquiry_repeat_use_drug_limit_detail (classify_id) COMMENT '中台分类末级id索引';


DROP TABLE IF EXISTS inquiry_drug_catalog_limit;
CREATE TABLE `inquiry_drug_catalog_limit`
(
  id            bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  province      varchar(100) NOT NULL DEFAULT '' COMMENT '省',
  province_code varchar(20)  NOT NULL DEFAULT '' COMMENT '省编码',
  city          varchar(100) NOT NULL DEFAULT '' COMMENT '市',
  city_code     varchar(20)  NOT NULL DEFAULT '' COMMENT '市编码',
  area          varchar(100) NOT NULL DEFAULT '' COMMENT '区',
  area_code     varchar(20)  NOT NULL DEFAULT '' COMMENT '区编码',
  hospital_pref varchar(64)  NOT NULL DEFAULT '' COMMENT '所属互联网医院',
  limit_type    int(11) NOT NULL DEFAULT '0' COMMENT '限制类型 1-黑名单 2-白名单',
  catalog_id    int(11) NOT NULL DEFAULT '0' COMMENT '分类id',
  yn            tinyint(4) NOT NULL DEFAULT '0' COMMENT '有效：0是，1否 ',
  creator       varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人',
  create_time   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater       varchar(64)  NOT NULL DEFAULT '' COMMENT '更新者',
  update_time   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted       bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 467311 DEFAULT CHARSET = utf8mb4 COMMENT = '问诊药品分类目录限制';
CREATE UNIQUE index uniq_province_city_area_hospital_catalog ON inquiry_drug_catalog_limit (province_code, city_code, area_code, hospital_pref, limit_type, catalog_id) COMMENT '限制用药唯一索引';

DROP TABLE IF EXISTS saas_dosage_limit;
CREATE TABLE `saas_dosage_limit`
(
  id                        bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  standard_id               varchar(64) NOT NULL DEFAULT '' COMMENT '标准库ID',
  general_name              varchar(64)          DEFAULT '' COMMENT '通用名',
  product_name              varchar(255)         DEFAULT '' COMMENT '商品名称',
  specifications            varchar(255)         DEFAULT '' COMMENT '规格',
  manufacturer              varchar(255)         DEFAULT '' COMMENT '生产厂家',
  total_dose_limit          int(11) DEFAULT '0' COMMENT '总使用剂量限制',
  whether_long_prescription tinyint(4) DEFAULT '0' COMMENT '是否长处方 0否, 1是',
  minimum_package_quantity  int(11) DEFAULT '0' COMMENT '最小包装数量',
  caution_level             int(11) DEFAULT '1' COMMENT '告警等级',
  yn                        tinyint(4) NOT NULL DEFAULT '1' COMMENT '有效：0是，1否 ',
  creator                   varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  create_time               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater                   varchar(64) NOT NULL DEFAULT '' COMMENT '更新者',
  update_time               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted                   bit(1)      NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 34961 DEFAULT CHARSET = utf8mb4 COMMENT = '用量参数限制';
CREATE INDEX idx_standard_id ON saas_dosage_limit (standard_id) COMMENT '中台分类末级id索引';

DROP TABLE IF EXISTS saas_usage_and_dosage_review_drug;
CREATE TABLE `saas_usage_and_dosage_review_drug`
(
  id                         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  usage_and_dosage_review_id bigint(20) NOT NULL COMMENT '用法用量审查表id',
  common_name                varchar(100) NOT NULL COMMENT '通用名',
  specification              varchar(100) NOT NULL COMMENT '规格',
  yn                         tinyint(4) NOT NULL DEFAULT '1' COMMENT '有效：0是，1否 ',
  creator                    varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人',
  create_time                datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater                    varchar(64)  NOT NULL DEFAULT '' COMMENT '更新者',
  update_time                datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted                    bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 19496 DEFAULT CHARSET = utf8mb4 COMMENT = '用法用量审查药品'
CREATE INDEX idx_usage_and_dosage_review_id ON saas_usage_and_dosage_review_drug (usage_and_dosage_review_id) COMMENT '用法用量审查表id索引';
CREATE INDEX idx_common_name ON saas_usage_and_dosage_review_drug (common_name) COMMENT '通用名索引';
CREATE INDEX idx_specification ON saas_usage_and_dosage_review_drug (specification) COMMENT '规格索引';


DROP TABLE IF EXISTS saas_usage_and_dosage_review;
CREATE TABLE `saas_usage_and_dosage_review`
(
  id                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  common_name         varchar(100) NOT NULL COMMENT '通用名',
  ingredient_dosage   varchar(50)  NOT NULL COMMENT '成分剂量',
  single_unit         varchar(20)  NOT NULL COMMENT '给药单位',
  directions          varchar(20)  NOT NULL COMMENT '给药途径',
  relation_rule_count int(11) DEFAULT '0' COMMENT '关联规则数量',
  relation_drug_count int(11) DEFAULT '0' COMMENT '关联规则数量',
  enable_status       int(11) DEFAULT '0' COMMENT '启用状态 0:启用 1:关闭',
  yn                  tinyint(4) NOT NULL DEFAULT '1' COMMENT '有效：0是，1否 ',
  creator             varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人',
  create_time         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater             varchar(64)  NOT NULL DEFAULT '' COMMENT '更新者',
  update_time         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted             bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 1080 DEFAULT CHARSET = utf8mb4 COMMENT = '用法用量审查'
CREATE INDEX idx_common_name ON saas_usage_and_dosage_review (common_name) COMMENT '通用名索引';
CREATE INDEX idx_specifications ON saas_usage_and_dosage_review (ingredient_dosage) COMMENT '成分索引';
CREATE INDEX idx_trelation_rule_count ON saas_usage_and_dosage_review (relation_rule_count) COMMENT '关联规则数量索引';
CREATE INDEX idx_relation_drug_count ON saas_usage_and_dosage_review (relation_drug_count) COMMENT '关联药品数量索引';
CREATE INDEX idx_enable_status ON saas_usage_and_dosage_review (enable_status) COMMENT '启用状态';


DROP TABLE IF EXISTS saas_usage_and_dosage_review_rule;
CREATE TABLE `saas_usage_and_dosage_review_rule`
(
  id                         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id' primary key,
  usage_and_dosage_review_id bigint(20) NOT NULL COMMENT '用法用量审查表id',
  min_age                    int(11) NOT NULL COMMENT '最小年龄',
  max_age                    int(11) NOT NULL COMMENT '最大年龄',
  min_single_dose            decimal(12, 6) NOT NULL COMMENT '单次剂量最低值',
  max_single_dose            decimal(12, 6) NOT NULL COMMENT '单次剂量最高值',
  use_frequency_list_json    varchar(1200) DEFAULT NULL COMMENT '用药频次集合json',
  age_risk_tips_switch       tinyint(4) NOT NULL DEFAULT '0' COMMENT '年龄风险提示开关 0开启 1关闭',
  yn                  tinyint(4) NOT NULL DEFAULT '1' COMMENT '有效：0是，1否 ',
  creator             varchar(64)  NOT NULL DEFAULT '' COMMENT '创建人',
  create_time         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updater             varchar(64)  NOT NULL DEFAULT '' COMMENT '更新者',
  update_time         datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  deleted             bit(1)       NOT NULL DEFAULT b'0' COMMENT '是否删除'
) ENGINE = InnoDB AUTO_INCREMENT = 2029 DEFAULT CHARSET = utf8mb4 COMMENT = '用法用量审查规则'
CREATE INDEX idx_usage_and_dosage_review_id ON saas_usage_and_dosage_review_rule (usage_and_dosage_review_id) COMMENT '用法用量审查表id索引';

DROP TABLE IF EXISTS inquiry_product_diagnosis_relation;
create table inquiry_product_diagnosis_relation
(
  id             bigint auto_increment comment '主键' primary key,
  diagnosis_code varchar(20)    default ''                not null comment '诊断编码',
  product_name   varchar(100)   default ''                not null comment '商品名称',
  cnt            int(12)        default 0                 not null comment '频率',
  rn             int(12)        default 1                 not null comment '排序',
  weight         decimal(10, 2) default 0.00              not null comment '权重',
  yn             tinyint        default 0                 null comment '是否有效 0 有效 1 无效',
  creator                  varchar(64) default ''                not null comment '创建者',
  create_time              datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater                  varchar(64) default ''                null comment '更新者',
  update_time              datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted                  bit         default b'0'              not null comment '是否删除'
) comment '商品诊断关联表';
alter table inquiry_product_diagnosis_relation add UNIQUE  index  idx_product_name_diagnosis_code(product_name, diagnosis_code) comment '药品诊断关系唯一索引';

-- 处方迁移记录表
DROP TABLE IF EXISTS saas_migration_prescription_record;
CREATE TABLE saas_migration_prescription_record
(
  id           bigint auto_increment comment '主键ID' primary key,
  organ_sign   varchar(64)  NOT NULL DEFAULT '' COMMENT '机构号',
  min_id       bigint       NOT NULL DEFAULT 0  COMMENT '最小ID',
  max_id       bigint       NOT NULL DEFAULT 0  COMMENT '最大ID',
  fail_reason  varchar(1000) NOT NULL DEFAULT '' COMMENT '失败原因',
  count  int(11)      NOT NULL DEFAULT 0 COMMENT '迁移成功数量',
  status       tinyint(2)   NOT NULL DEFAULT 0 COMMENT '处理状态 0-处理成功 1-MQ发送失败 2-ES存储失败',
  create_time  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  update_time  datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT ='处方迁移记录表';

-- 创建索引
CREATE INDEX idx_min_id ON saas_migration_prescription_record (min_id) COMMENT '最小id';
CREATE INDEX idx_max_id ON saas_migration_prescription_record (max_id) COMMENT '最大id';
CREATE INDEX idx_create_time ON saas_migration_prescription_record (create_time) COMMENT '创建时间索引';